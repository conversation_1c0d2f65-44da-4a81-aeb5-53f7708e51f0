{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["**/env.d.ts", "playground/**/*", "playground/**/*.vue", "packages/**/*.vue", "packages/**/*.ts", "packages/**/*.tsx"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@matechat/playground/*": ["./playground/*"], "@matechat/core/*": ["./packages/components/*"]}}}