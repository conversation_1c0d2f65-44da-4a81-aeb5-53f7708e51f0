@import "devui-theme/styles-var/devui-var.scss";
.mc-markdown-render :deep() {
  ul,
  ol {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  ul {
    list-style-type: disc;
    padding-left: 16px;
  }

  ol {
    list-style-type: decimal;
    padding-left: 16px;
  }

  p {
    line-height: 28px;
    margin: 0;
    padding: 0;
    overflow-wrap: break-word;
  }

  h1 {
    font-size: 32px;
    line-height: 40px;
    overflow-wrap: break-word;
  }

  h3 {
    line-height: 28px;
    font-size: 20px;
    overflow-wrap: break-word;
  }

  table {
    margin-bottom: 10px;
    border-collapse: collapse;
    display: table;
  }

  td,
  th {
    padding: 5px 10px;
    border: 1px solid $devui-dividing-line;
    background-color: $devui-base-bg;
  }

  caption {
    border: 1px dashed $devui-line;
    border-bottom: 0;
    padding: 3px;
    text-align: center;
  }

  th {
    border-top: 1px solid $devui-dividing-line;
    background-color: $devui-global-bg;
  }

  td p {
    margin: 0;
    padding: 0;
  }

  .h1,
  .h2,
  .h3,
  .h4,
  .h5,
  .h6,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.1;
  }

  blockquote {
    padding: 0 8px;
    margin: 0;
    color: $devui-text-weak;
    border-left: 5px solid $devui-dividing-line;
  }

  a {
    color: $devui-link;
    text-decoration: underline;
    cursor: pointer;

    &:hover {
      color: $devui-link-active;
    }
  }

  img {
    max-width: 100%;
  }
}