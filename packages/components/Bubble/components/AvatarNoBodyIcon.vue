<template>
  <svg
    :style="{
      width: `${width}px`,
      height: `${height}px`,
      verticalAlign: 'middle',
    }"
    viewBox="0 0 30 30"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
      <path
        :d="`M22.31,19.2474562 L22.31,21.9974562 L20.81,21.9974562 L20.81, 19.2474562 L18.06,19.2474562 L18.06,17.7474562 
          L20.81,17.7474562 L20.81, 14.9974562 L22.31,14.9974562 L22.31,17.7474562 L25.06,17.7474562 L25.06, 19.2474562 
          L22.31,19.2474562 Z M14.9297866,14.9974562 C12.4445053, 14.9974562 10.4297866,12.9827376 10.4297866,10.4974562 
          C10.4297866, 8.01217483 12.4445053,5.9974562 14.9297866,5.9974562 C17.415068, 5.9974562 19.4297866,8.01217483 
          19.4297866,10.4974562 C19.4297866, 12.9827376 17.415068,14.9974562 14.9297866,14.9974562 Z M19.6797866, 20.2474562 
          L19.6797866,21.9971623 L6.93,21.9960092 C6.93,18.0096715 10.8192296, 15.4974562 14.9297866,15.4974562 C16.4608397,15.4974562 
          17.9612467,15.983021 19.2414296, 16.7474562 L17.06,16.7474562 L17.06,20.2474562 L19.6797866,20.2474562 Z`"
        fill="#959EB2"
      ></path>
      <path
        :d="`M0,15 L1,15 C1,15.6118966 1.03919561,16.2186526 1.11683409,
            16.8178549 L0.125124012,16.9463505 C0.0425740367,16.309242 0,
            15.6595925 0,15 Z M0.503241262,18.867175 L1.46961749,18.6100428 C1.62594651,
            19.1975718 1.8203704,19.7729973 2.05141295,20.3332823 L1.12693074,
            20.7145074 C0.880599065,20.1171459 0.67172715,19.500393 0.503241262,
            18.867175 Z M1.99122134,22.4730778 L2.85786221,21.9741453 C3.16078316,
            22.5003161 3.49772502,23.0063252 3.86631639,23.4889602 L3.0715754,
            24.0959089 C2.6777461,23.5802273 2.31659753,23.0382531 1.99122134,
            22.4730778 Z M4.38894559,25.6021078 L5.09634867,24.8952974 C5.52582519,
            25.3251341 5.98272136,25.7268214 6.46397085,26.0975793 L5.85367498,
            26.8897529 C5.33779918,26.4923186 4.84851395,26.0620615 4.38894559,
            25.6021078 Z M7.4424647,27.9597887 L7.94703323,27.096417 C8.47111119,
            27.4026968 9.0146817,27.6746032 9.57453904,27.9101878 L9.18668461,
            28.8319084 C8.58423501,28.5784013 8.00181692,28.2866833 7.4424647,
            27.9597887 Z M11.0671021,29.4791103 L11.3286494,28.5139196 C11.9126912,
            28.6721832 12.5080563,28.7925378 13.1119738,28.8738935 L12.9784667,
            29.8649413 C12.3271613,29.7772019 11.6891102,29.647662 11.0671021,
            29.4791103 Z M15,30 C14.9951965,30 14.9903936,29.9999977 14.9855912,
            30 L14.9865313,28.9999937 C14.9929361,28.9999987 14.9929361,
            28.9999987 14.999296,29 C15.6071552,29 16.2093364,28.9614092 16.8041774,
            28.8849313 L16.9316965,29.8767674 C16.2992813,29.9580762 15.6545362,
            30 15,30 Z M18.8545762,29.5001051 L18.5982903,28.5335041 C19.1860387,
            28.3776677 19.7617059,28.1837179 20.3222555,27.9531286 L20.7026875,
            28.8779375 C20.1050484,29.123784 19.4880358,29.3321488 18.8545762,
            29.5001051 Z M22.4608087,28.0158343 L21.9626951,27.1487226 C22.4893928,
            26.8461604 22.9959399,26.5095265 23.479119,26.1411926 L24.0853678,
            26.9364676 C23.5691101,27.3300178 23.0265585,27.6908386 22.4608087,
            28.0158343 Z M25.5921583,25.6209863 L24.8860071,24.9129252 C25.3161421,
            24.4839504 25.7181674,24.0275419 26.0893023,23.5467621 L26.8808873,
            24.1578212 C26.4830546,24.6731862 26.0524368,25.1619493 25.5921583,
            25.6209863 Z M27.9526445,22.5697466 L27.0897495,22.0643633 C27.3964738,
            21.5406601 27.6688549,20.9974409 27.904942,20.4379104 L28.8262855,
            20.8266601 C28.5722411,21.4287497 28.2800163,22.0107897 27.9526445,
            22.5697466 Z M29.4756977,18.9454696 L28.5107363,18.6830777 C28.6695136,
            18.099165 28.7903877,17.5039035 28.8722662,16.9000659 L29.8631978,
            17.0344333 C29.7748946,17.6856516 29.6447979,18.3235936 29.4756977,
            18.9454696 Z M30,15 C30,15.0093541 29.9999914,15.0187063 29.9999743,
            15.0280564 L28.999976,15.0262257 C28.9999956,15.0134169 28.9999956,
            15.0134169 29,15.0006466 C29,14.3970304 28.9619395,13.7989704 28.8865088,
            13.208136 L29.8784576,13.0814959 C29.9586571,13.7096843 30,14.3500145 30,
            15 Z M29.5038108,11.1594275 L28.5369608,11.4147728 C28.3816616,
            10.8267413 28.1882232,10.2507676 27.9581175,9.68988857 L28.8832852,
            9.31033004 C29.1286141,9.90831524 29.3364318,10.5256569 29.5038108,
            11.1594275 Z M28.0228788,7.55146763 L27.1552968,8.0487618 C26.853241,
            7.52179373 26.5170958,7.0149453 26.1492295,6.53143425 L26.9450761,
            5.92593594 C27.3381231,6.44254364 27.6984223,6.98541919 28.0228788,
            7.55146763 Z M25.6309089,4.41780044 L24.9221905,5.12329189 C24.4935639,
            4.69270467 24.0374698,4.2902078 23.5569705,3.9185855 L24.1687554,
            3.12756133 C24.6838129,3.52591118 25.1722392,3.95703271 25.6309089,
            4.41780044 Z M22.5819506,2.05451093 L22.0757531,2.91692851 C21.5523008,
            2.60968727 21.0092999,2.33677531 20.4499564,2.10014728 L20.8395722,
            1.17916981 C21.4414531,1.43379309 22.0232574,1.72658499 22.5819506,
            2.05451093 Z M18.9594329,0.528106464 L18.696103,1.4928123 C18.1122842,
            1.33345081 17.517086,1.21199245 16.9132846,1.12953261 L17.0485964,
            0.138729543 C17.6997753,0.227659622 18.3376514,0.358382523 18.9594329,
            0.528106464 Z M15,-1.11022302e-16 C15.0139048,-1.11022302e-16 15.0278052,
            1.892004e-05 15.0417011,5.673578e-05 L15.0389797,1.00005303 C15.0197685,
            1.00000934 15.0197685,1.00000934 15.0005868,1.00000001 C14.4012162,
            1 13.8072783,1.03753392 13.2204527,1.11192427 L13.0946918,0.119863691 C13.7186509,
            0.0407660189 14.3545651,-1.11022302e-16 15,-1.11022302e-16 Z M11.1720354,
            0.492865376 L11.4265338,1.45993857 C10.8386468,1.61464899 10.2627801,
            1.80746996 9.70196444,2.03693489 L9.32327455,1.11141127 C9.92118954,
            0.86676666 10.5384173,0.659610554 11.1720354,0.492865376 Z M7.56375123,
            1.97008801 L8.06022548,2.83813946 C7.53298826,3.13968806 7.02583975,
            3.47534367 6.54199783,3.84274131 L5.93725081,3.04632368 C6.4542073,
            2.65378102 6.99740556,2.29400434 7.56375123,1.97008801 Z M4.42776842,
            4.35917772 L5.13259949,5.06855291 C4.70156141,5.49682984 4.29859416,
            5.95260763 3.92648544,6.43282469 L3.13602297,5.82031423 C3.53488885,
            5.30556625 3.96651291,4.81747851 4.42776842,4.35917772 Z M2.0616775,
            7.40585279 L2.92361696,7.91286401 C2.61586086,8.43606175 2.34241977,
            8.97884042 2.10525197,9.53799293 L1.18464153,9.14751063 C1.43984243,
            8.54584288 1.73319965,7.96427837 2.0616775,7.40585279 Z M0.531541456,
            11.0280046 L1.49601653,11.2921785 C1.33614878,11.8758413 1.21417484,
            12.4709081 1.13119244,13.074601 L0.140507978,12.9384235 C0.230001486,12.2873626 0.361276825,11.6496255 0.531541456,11.0280046 Z`"
        fill="#CACFD8"
        fill-rule="nonzero"
      ></path>
    </g>
  </svg>
</template>

<script setup lang="ts">
defineProps({
  width: {
    type: Number,
    default: 16,
  },
  height: {
    type: Number,
    default: 16,
  },
});
</script>
