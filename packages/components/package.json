{"name": "@matechat/core", "version": "1.5.1", "license": "MIT", "description": "前端智能化场景解决方案UI库，轻松构建你的AI应用。", "keywords": ["AI", "GPT", "Cha<PERSON>", "vue-components", "vue3", "devui"], "homepage": "https://matechat.gitcode.com/", "repository": {"type": "git", "url": "***************:DevCloudFE/MateChat.git"}, "module": "mate-chat.js", "types": "index.d.ts", "sideEffects": ["**/*.css"], "dependencies": {"@floating-ui/dom": "^1.6.12", "@vue/shared": "^3.5.13", "devui-theme": "^0.0.7", "lodash-es": "^4.17.21", "highlight.js": "^11.11.0", "markdown-it": "12.2.0", "xss": "^1.0.15"}, "peerDependencies": {"vue": "^3.5.13"}}