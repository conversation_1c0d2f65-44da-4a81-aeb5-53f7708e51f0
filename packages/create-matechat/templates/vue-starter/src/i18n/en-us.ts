export default {
  navbar: {
    chat: "Chat",
    systemSetting: "System Setting",
  },
  history: {
    chatHistory: "ChatHistory",
    searchChat: "Search Chat",
    deleteHistoryTipTitle: "Delete History Tips",
    deleteHistoryConfirmTxt: "Are you sure to delete this conversation?",
  },
  knowledge: {
    addKnowledge: "Add Knowledge",
  },
  welcome: {
    welcomeTo: "Welcome to",
    description1:
      "MateChat can assist R&D personnel in coding, querying knowledge and related work information, writing documents, etc.",
    description2:
      "As an AI model, the answers provided by MateChat may not always be definitive or accurate, but your feedback can help MateChat do better.",
    guessYouWantAsk: "Guess you want to ask",
    change: "Change",
  },
  theme: {
    themeTitle: "Theme",
  },
  input: {
    disclaimer:
      "The content is generated by AI and its accuracy and completeness cannot be guaranteed. It is for reference only",
    privacyStatement: "Privacy Statement",
  },
  delete: "Delete",
  deleteSuccess: "Delete successfully",
  confirm: "Confirm",
  cancel: "Cancel",
  confirmDelete: "Delete Confirmation",
  underDevelop: "Under development",
  appendix: "Appendix",
  audio: "Audio",
  agent: "Agent",
  thesaurus: "Prompts",
  onlineSearch: "OnlineSearch",
  chatSetting: "Chat Setting",
  newChat: "New Conversation",
  noData: "No Data",
};
