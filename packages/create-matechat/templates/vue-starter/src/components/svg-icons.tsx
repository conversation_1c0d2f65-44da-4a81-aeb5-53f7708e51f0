export function PromptsIcon() {
  return (
    <svg
      viewBox="0 0 10.5707 14.6408"
      xmlns="http://www.w3.org/2000/svg"
      width="10.5706787"
      height="14.6408081"
      fill="none"
    >
      <rect width="16" height="16" x="-2" y="-0.92980957" />
      <path
        d="M10.0001 0.570618L4 14.0702"
        stroke="var(--devui-text)"
        stroke-width="1"
      />
      <path
        d="M2.5 0.0701904L1.80574 1.85894L3.55271e-14 2.581L1.80574 3.27758L2.5 5.07019L3.1486 3.27758L5 2.581L3.1486 1.85894L2.5 0.0701904Z"
        fill="var(--devui-text)"
        fill-rule="evenodd"
      />
    </svg>
  );
}

export function AddKnowledgeIcon() {
  return (
    <svg
      viewBox="0 0 18.3005 19.3604"
      xmlns="http://www.w3.org/2000/svg"
      width="18.3004761"
      height="19.3603516"
      fill="none"
    >
      <rect width="16" height="16" x="0" y="2.84014893" />
      <path
        d="M14.75 2.84015L14.5417 3.37553L14 3.59171L14.5417 3.8002L14.75 4.33668L14.9446 3.8002L15.5 3.59171L14.9446 3.37553L14.75 2.84015ZM11 3.36088C11.7315 3.36088 12.3713 3.75369 12.7201 4.33973L5.4999 4.3399C3.56694 4.3399 1.99998 5.90692 1.99998 7.83988L1.99994 16.0929C1.40216 15.7471 1 15.1007 1 14.3605L1 7.86088C1 5.3756 3.01472 3.36088 5.5 3.36088L11 3.36088ZM13 5.36081C14.1046 5.36081 15 6.25617 15 7.36074L15 16.3604C15 17.465 14.1046 18.3604 13 18.3604L5 18.3604C3.89545 18.3604 3 17.465 3 16.3604L3 7.86081C3 6.4801 4.11929 5.36081 5.5 5.36081L13 5.36081ZM4 16.3604C4 16.8732 4.38605 17.2959 4.88339 17.3537L5 17.3604L13 17.3604C13.5128 17.3604 13.9355 16.9744 13.9933 16.477L14 16.3604L14 7.36074C14 6.84795 13.614 6.42523 13.1166 6.36749L13 6.36078L5.50016 6.35997C4.67167 6.35997 4 7.03148 4 7.85997L4 16.3604ZM6.5 8.56017L5.5 8.56017L5.5 9.56013L6.5 9.56013L6.5 8.56017ZM8.5 8.56017L7.5 8.56017L7.5 9.56013L8.5 9.56013L8.5 8.56017ZM9.5 8.56017L10.5 8.56017L10.5 9.56013L9.5 9.56013L9.5 8.56017ZM12 11.8601L6 11.8601C5.72388 11.8601 5.5 12.084 5.5 12.3601C5.5 12.6362 5.72388 12.8601 6 12.8601L12 12.8601C12.2762 12.8601 12.5 12.6362 12.5 12.3601C12.5 12.084 12.2762 11.8601 12 11.8601ZM5.5 14.86C5.5 14.5839 5.72388 14.36 6 14.36L11 14.36C11.2762 14.36 11.5 14.5839 11.5 14.86C11.5 15.1361 11.2762 15.36 11 15.36L6 15.36C5.72388 15.36 5.5 15.1361 5.5 14.86Z"
        fill="var(--devui-text)"
        fill-rule="evenodd"
      />
      <path
        d="M14.9124 4.30431L14.3706 4.52049L14 3.59171L14.3592 2.65844L15.3139 3.02584L15.6822 3.97481L14.75 4.33668L13.8099 3.99572L14.1617 3.02596L15.1486 2.6555L15.5 3.59171L15.1373 4.52361L14.1595 4.14305L13.8101 3.18173L14.75 2.84015L15.682 3.20267L15.3161 4.1432L14.9124 4.30431ZM14.1711 2.44675L14.5417 3.37553L13.6098 3.01301L14.7818 -4.76837e-07L15.8844 3.03395L14.9446 3.37553L15.3073 2.44363L18.3005 3.60863L15.296 4.73641L14.9446 3.8002L15.8847 4.14116L14.7818 7.18193L13.6095 4.16207L14.5417 3.8002L14.1826 4.73347L11.2596 3.60857L14.1711 2.44675ZM12.7201 5.33973L5.49993 5.3399C5.1604 5.3399 4.83613 5.40525 4.52713 5.53595C4.22817 5.6624 3.96319 5.84113 3.7322 6.07213C3.50121 6.30312 3.32248 6.56811 3.19603 6.86708C3.06533 7.1761 2.99998 7.50036 2.99998 7.83988L2.99993 17.8266L1.49919 16.9585C1.04338 16.6948 0.681752 16.3361 0.414322 15.8824C0.138107 15.4138 0 14.9065 0 14.3605L-2.38419e-07 7.86088C-2.38419e-07 7.11534 0.144209 6.40163 0.432628 5.71973C0.710994 5.0616 1.10376 4.47895 1.61091 3.9718C2.11807 3.46464 2.70071 3.07188 3.35884 2.79351C4.04074 2.50509 4.75446 2.36088 5.5 2.36088L11 2.36088C11.5394 2.36088 12.0415 2.49591 12.5061 2.76598C12.9557 3.0273 13.3135 3.38143 13.5794 3.82836L14.4787 5.33969L12.7201 5.33973ZM12.72 3.33973L12.7201 4.33973L11.8607 4.8511C11.7716 4.70133 11.6517 4.58266 11.5011 4.49511C11.3471 4.40562 11.1801 4.36088 11 4.36088L5.5 4.36088C5.02484 4.36088 4.57083 4.45243 4.13795 4.63552C3.71933 4.81258 3.34839 5.06274 3.02513 5.38601C2.70186 5.70928 2.4517 6.08022 2.27464 6.49883C2.09155 6.93171 2 7.38572 2 7.86088L2 14.3605C2 14.5428 2.04576 14.7115 2.13728 14.8668C2.22688 15.0188 2.34801 15.1389 2.50068 15.2273L1.99994 16.0929L0.999939 16.0929L0.999982 7.83988C0.999982 7.22998 1.11799 6.64602 1.35402 6.08799C1.58177 5.5495 1.90309 5.07282 2.31797 4.65793C2.73285 4.24304 3.20953 3.92171 3.74801 3.69395C4.30604 3.45791 4.89 3.3399 5.49988 3.3399L12.72 3.33973ZM16 7.36074L16 16.3604C16 16.7669 15.9213 17.1562 15.7638 17.5285C15.612 17.8875 15.3978 18.2053 15.1213 18.4817C14.8448 18.7582 14.5271 18.9724 14.168 19.1242C13.7958 19.2817 13.4064 19.3604 13 19.3604L5 19.3604C4.59356 19.3604 4.20422 19.2817 3.83199 19.1242C3.47295 18.9724 3.15519 18.7582 2.87871 18.4817C2.60221 18.2053 2.38804 17.8875 2.23617 17.5285C2.07872 17.1562 2 16.7669 2 16.3604L2 7.86081C2 7.38654 2.09182 6.93232 2.27546 6.49815C2.45262 6.0793 2.70251 5.70856 3.02513 5.38594C3.34774 5.06332 3.71848 4.81343 4.13734 4.63627C4.57151 4.45263 5.02573 4.36081 5.5 4.36081L13 4.36081C13.4065 4.36081 13.7958 4.43953 14.168 4.59696C14.5271 4.74881 14.8448 4.96297 15.1213 5.23944C15.3978 5.51592 15.612 5.83367 15.7638 6.19271C15.9213 6.56494 16 6.95428 16 7.36074ZM14 7.36074C14 7.22467 13.9739 7.09503 13.9218 6.97182C13.8713 6.85231 13.7997 6.74627 13.7071 6.65369C13.6145 6.5611 13.5085 6.48954 13.389 6.43899C13.2657 6.38687 13.1361 6.36081 13 6.36081L5.5 6.36081C5.29611 6.36081 5.10159 6.39997 4.91644 6.47828C4.7371 6.55413 4.57807 6.66143 4.43934 6.80015C4.30061 6.93888 4.19332 7.09792 4.11747 7.27725C4.03916 7.4624 4 7.65692 4 7.86081L4 16.3604C4 16.4965 4.02606 16.6261 4.07817 16.7494C4.12872 16.8689 4.20029 16.9749 4.29288 17.0675C4.38547 17.1601 4.49153 17.2316 4.61105 17.2822C4.73427 17.3343 4.86392 17.3604 5 17.3604L13 17.3604C13.1361 17.3604 13.2657 17.3343 13.389 17.2822C13.5085 17.2316 13.6145 17.1601 13.7071 17.0675C13.7997 16.9749 13.8713 16.8689 13.9218 16.7494C13.9739 16.6262 14 16.4965 14 16.3604L14 7.36074ZM4.94087 16.3553L5.05747 16.3621L5 17.3604L5 16.3604L13 16.3604L13 16.3616L13.9933 16.477L12.9949 16.4195L13.0017 16.3029L14 16.3604L13 16.3604L13 7.36074L13.0013 7.36082L13.1166 6.36749L13.0592 7.36584L12.9425 7.35913L13 6.36078L12.9999 7.36078L5.50005 7.35997C5.36198 7.35997 5.24412 7.40879 5.14645 7.50644C5.04882 7.60406 5 7.7219 5 7.85997L5 16.3604L4.99871 16.3604L4.88339 17.3537L4.94087 16.3553ZM4.82592 18.352L4.79692 18.3504L4.76808 18.347C4.52209 18.3185 4.28955 18.2466 4.07043 18.1315C3.85781 18.0198 3.6712 17.8744 3.51061 17.6952C3.34902 17.515 3.22433 17.3124 3.13653 17.0874C3.04551 16.8542 3 16.6119 3 16.3604L3 7.85997C3 7.5213 3.06564 7.1968 3.19691 6.88648C3.32348 6.58726 3.50196 6.32247 3.73234 6.09212C3.96271 5.86179 4.22751 5.68335 4.52675 5.55681C4.83705 5.42558 5.16156 5.35997 5.50027 5.35997L13.0288 5.36078L13.2031 5.37081L13.232 5.37416C13.4779 5.40272 13.7105 5.47456 13.9296 5.58969C14.1422 5.7014 14.3288 5.84683 14.4894 6.02596C14.651 6.2062 14.7757 6.4088 14.8635 6.63378C14.9545 6.86698 15 7.1093 15 7.36074L15 16.3892L14.99 16.5635L14.9866 16.5924C14.958 16.8384 14.8862 17.0709 14.7711 17.29C14.6594 17.5026 14.5139 17.6892 14.3348 17.8498C14.1546 18.0114 13.9519 18.1361 13.727 18.2239C13.4938 18.3149 13.2514 18.3604 13 18.3604L4.97124 18.3604L4.82592 18.352ZM6.5 8.56017L6.5 9.56013L5.5 9.56013L5.5 8.56013L6.5 8.56013L6.5 9.56013L5.5 9.56013L5.5 8.56017L6.5 8.56017L6.5 9.56017L5.5 9.56017L5.5 8.56017L6.5 8.56017ZM4.5 7.56017L7.5 7.56017L7.5 10.5601L4.5 10.5601L4.5 8.56017L4.5 7.56017ZM8.5 8.56017L8.5 9.56013L7.5 9.56013L7.5 8.56013L8.5 8.56013L8.5 9.56013L7.5 9.56013L7.5 8.56017L8.5 8.56017L8.5 9.56017L7.5 9.56017L7.5 8.56017L8.5 8.56017ZM6.5 7.56017L9.5 7.56017L9.5 10.5601L6.5 10.5601L6.5 8.56017L6.5 7.56017ZM11.5 8.56017L11.5 10.5601L8.5 10.5601L8.5 7.56017L11.5 7.56017L11.5 8.56017ZM9.5 8.56017L10.5 8.56017L10.5 9.56017L9.5 9.56017L9.5 8.56017L10.5 8.56017L10.5 9.56013L9.5 9.56013L9.5 8.56013L10.5 8.56013L10.5 9.56013L9.5 9.56013L9.5 8.56017ZM6 12.8601C6.13807 12.8601 6.25592 12.8113 6.35355 12.7137C6.45118 12.616 6.5 12.4982 6.5 12.3601C6.5 12.222 6.45118 12.1041 6.35355 12.0065C6.25592 11.9089 6.13807 11.8601 6 11.8601L12 11.8601C11.8619 11.8601 11.7441 11.9089 11.6464 12.0065C11.5488 12.1041 11.5 12.222 11.5 12.3601C11.5 12.4982 11.5488 12.616 11.6464 12.7137C11.7441 12.8113 11.8619 12.8601 12 12.8601L6 12.8601ZM6 10.8601L12 10.8601C12.203 10.8601 12.3977 10.8995 12.5842 10.9784C12.7638 11.0543 12.9226 11.1614 13.0607 11.2994C13.1987 11.4375 13.3058 11.5963 13.3817 11.7759C13.4606 11.9624 13.5 12.1571 13.5 12.3601C13.5 12.5631 13.4606 12.7578 13.3817 12.9442C13.3058 13.1238 13.1987 13.2826 13.0607 13.4207C12.9226 13.5588 12.7638 13.6658 12.5842 13.7418C12.3977 13.8206 12.203 13.8601 12 13.8601L6 13.8601C5.79699 13.8601 5.60227 13.8206 5.41582 13.7418C5.23625 13.6658 5.07743 13.5588 4.93936 13.4207C4.80128 13.2827 4.69426 13.1238 4.61831 12.9443C4.53944 12.7578 4.5 12.5631 4.5 12.3601C4.5 12.1571 4.53944 11.9623 4.61831 11.7759C4.69426 11.5963 4.80128 11.4375 4.93936 11.2994C5.07743 11.1614 5.23625 11.0544 5.41582 10.9784C5.60227 10.8995 5.79699 10.8601 6 10.8601ZM6 13.36L11 13.36C11.203 13.36 11.3977 13.3994 11.5842 13.4783C11.7638 13.5543 11.9226 13.6613 12.0607 13.7994C12.1987 13.9374 12.3058 14.0963 12.3817 14.2758C12.4606 14.4623 12.5 14.657 12.5 14.86C12.5 15.063 12.4606 15.2577 12.3817 15.4442C12.3058 15.6237 12.1987 15.7826 12.0607 15.9206C11.9226 16.0587 11.7638 16.1657 11.5842 16.2417C11.3977 16.3206 11.203 16.36 11 16.36L6 16.36C5.79699 16.36 5.60227 16.3205 5.41582 16.2417C5.23625 16.1657 5.07743 16.0587 4.93936 15.9207C4.80128 15.7826 4.69426 15.6238 4.61831 15.4442C4.53944 15.2577 4.5 15.063 4.5 14.86C4.5 14.657 4.53944 14.4623 4.61831 14.2758C4.69426 14.0962 4.80128 13.9374 4.93936 13.7993C5.07743 13.6613 5.23625 13.5543 5.41582 13.4783C5.60227 13.3994 5.79699 13.36 6 13.36ZM6 15.36C6.13807 15.36 6.25592 15.3112 6.35355 15.2136C6.45118 15.116 6.5 14.9981 6.5 14.86C6.5 14.7219 6.45118 14.604 6.35355 14.5064C6.25592 14.4088 6.13807 14.36 6 14.36L11 14.36C10.8619 14.36 10.7441 14.4088 10.6464 14.5064C10.5488 14.6041 10.5 14.7219 10.5 14.86C10.5 14.9981 10.5488 15.1159 10.6464 15.2136C10.7441 15.3112 10.8619 15.36 11 15.36L6 15.36Z"
        fill="var(--devui-text)"
        fill-opacity="0"
        fill-rule="nonzero"
      />
    </svg>
  );
}

export function ExpandIcon() {
  return (
    <svg
      viewBox="0 0 5 9"
      xmlns="http://www.w3.org/2000/svg"
      width="5"
      height="9"
      fill="none"
    >
      <rect
        width="16"
        height="16"
        x="0"
        y="0"
        transform="matrix(0,1,1,0,-5.5,-3.5)"
      />
      <path
        d="M0.146447 0.146447C0.320013 -0.0271197 0.589437 -0.0464049 0.784305 0.0885912L0.853553 0.146447L4.5 3.793L8.14645 0.146447C8.32001 -0.0271197 8.58944 -0.0464049 8.78431 0.0885912L8.85355 0.146447C9.02712 0.320013 9.0464 0.589437 8.91141 0.784305L8.85355 0.853553L4.85355 4.85355C4.67999 5.02712 4.41056 5.04641 4.21569 4.91141L4.14645 4.85355L0.146447 0.853553C-0.0488155 0.658291 -0.0488155 0.341709 0.146447 0.146447Z"
        fill="rgb(201,201,201)"
        fill-rule="evenodd"
        transform="matrix(0,-1,-1,0,5,9)"
      />
    </svg>
  );
}
