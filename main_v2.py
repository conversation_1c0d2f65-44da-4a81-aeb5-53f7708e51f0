#!/usr/bin/env python3
"""
LangGraph Agent 主程序 v2.0 - 使用新的核心架构
保持与原main.py完全兼容的CLI功能，但使用重构后的核心组件
"""

import asyncio
import os
import sys
from typing import Optional

from langchain_core.messages import HumanMessage, AIMessage, ToolMessage

# 导入新的核心组件
from core.agent_core import agent_core
from services.config_manager import config_manager
from services.error_handler import error_handler


def show_welcome(tools=None):
    """显示欢迎信息"""
    print("\n🤖 LangGraph Agent 交互式助手 v2.0 (混合架构版本)")
    print("=" * 60)
    print("✅ 对话历史将自动保存")
    print("✅ 程序重启后可以继续之前的对话")
    print("✅ 支持CLI和Web API混合模式")
    
    if tools:
        print(f"🛠️  已加载 {len(tools)} 个工具")
    
    print("\n📋 可用命令:")
    print("  • 'new' - 开始新对话")
    print("  • 'resume <thread_id>' - 恢复到指定会话")
    print("  • 'history' - 查看对话历史")
    print("  • 'help' - 查看帮助")
    print("  • 'tools' - 查看可用工具")
    print("  • 'clear' - 清屏")
    print("  • 'quit' 或 'exit' - 退出程序")


def show_help(tools=None):
    """显示帮助信息"""
    print("\n📖 帮助信息:")
    print("这是一个基于 LangGraph 的智能助手 v2.0，具有以下特性：")
    print("• 🧠 智能对话：支持复杂的多轮对话")
    print("• 🛠️ 工具调用：可以使用各种外部工具")
    print("• 💾 持久化：对话历史自动保存")
    print("• 🔄 会话管理：支持多个独立会话")
    print("• 🌐 混合架构：支持CLI和Web API双模式")
    
    if tools:
        print(f"\n🛠️ 当前可用工具 ({len(tools)} 个):")
        for tool in tools[:5]:  # 只显示前5个
            print(f"  • {tool['name']}: {tool['description'][:50]}...")
        if len(tools) > 5:
            print(f"  ... 还有 {len(tools) - 5} 个工具")


async def run_agent_with_persistence_v2(query: str, thread_id: str):
    """使用新架构运行Agent - 保持与原版本兼容的输出格式"""
    print(f"\n🚀 开始处理查询: '{query}' (会话: {thread_id})")

    try:
        # 使用新的核心组件进行流式处理
        stream_generator = agent_core.stream_message(query, thread_id)
        async for output in stream_generator:
            print(f"\n--- 状态更新 ---")
            msg_type = output["type"]
            content = output["content"]
            tool_calls = output.get("tool_calls")

            print(f"📨 新消息类型: {msg_type}")

            if msg_type == "HumanMessage":
                print(f"👤 用户: {content}")
            elif msg_type == "AIMessage":
                if tool_calls:
                    print(f"🤖 AI -> 调用工具: {[tc['name'] for tc in tool_calls]}")
                    for tc in tool_calls:
                        print(f"   工具: {tc['name']}, 参数: {tc['args']}")
                else:
                    print(f"🤖 AI -> 最终回复:\n{content}")
            elif msg_type == "ToolMessage":
                print(f"🔧 工具结果: {content[:100]}...")

    except Exception as e:
        print(f"\n💥 Agent执行出错: {e}")
        import traceback
        traceback.print_exc()


async def interactive_loop_with_persistence_v2():
    """使用新架构的交互式对话循环 - 保持与原版本完全兼容"""
    try:
        # 初始化核心组件
        print("--- 初始化Agent v2.0 (混合架构) ---")
        await agent_core.initialize()
        
        # 获取工具信息
        tools = agent_core.get_available_tools()
        print(f"✅ 已加载 {len(tools)} 个工具")
        print("--- 工作流构建完成 (已集成持久化) ---")
        
        # 显示持久化信息
        checkpointer_type = type(agent_core.checkpointer).__name__
        print(f"📝 检查点存储: {checkpointer_type}")
        
        show_welcome(tools)
        
        # 创建新会话
        thread_id = agent_core.create_session()
        
        while True:
            try:
                print("\n" + "-" * 40)
                user_input = input("💬 请输入您的问题: ").strip()

                if not user_input:
                    continue

                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 再见！感谢使用 LangGraph Agent 助手 v2.0！")
                    break
                elif user_input.lower() == 'new':
                    thread_id = agent_core.create_session()
                    print("✅ 已开始新对话")
                    continue
                elif user_input.lower().startswith('resume '):
                    # 恢复到指定会话：resume thread_id
                    parts = user_input.split(' ', 1)
                    if len(parts) == 2:
                        target_thread_id = parts[1].strip()
                        thread_id = agent_core.session_manager.resume_session(target_thread_id)
                        print("✅ 已恢复到指定会话")
                    else:
                        print("❌ 请提供会话ID，格式：resume <thread_id>")
                    continue
                elif user_input.lower() == 'history':
                    # 获取对话历史
                    try:
                        history = await agent_core.get_session_history(thread_id, limit=10)
                        if history:
                            print(f"\n📚 当前会话历史 ({len(history)} 条消息):")
                            for i, msg in enumerate(history, 1):
                                msg_type = "👤" if msg["type"] == "HumanMessage" else "🤖" if msg["type"] == "AIMessage" else "🔧"
                                content = msg["content"][:50] + "..." if len(msg["content"]) > 50 else msg["content"]
                                print(f"  {i}. {msg_type} {content}")
                        else:
                            print("📚 当前会话暂无历史记录")
                    except Exception as e:
                        print(f"❌ 获取历史记录失败: {e}")
                    continue
                elif user_input.lower() == 'help':
                    show_help(tools)
                    continue
                elif user_input.lower() == 'tools':
                    print(f"\n🛠️ 可用工具 ({len(tools)} 个):")
                    for tool in tools:
                        print(f"  • {tool['name']}: {tool['description']}")
                    continue
                elif user_input.lower() == 'clear':
                    os.system('clear' if os.name == 'posix' else 'cls')
                    show_welcome(tools)
                    continue

                # 处理用户问题
                await run_agent_with_persistence_v2(user_input, thread_id)

            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"\n💥 发生错误: {e}")
                print("请重试或输入 'quit' 退出")
                
    except Exception as e:
        print(f"\n💥 初始化失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主函数"""
    try:
        # 验证配置
        validation_results = config_manager.validate_config()
        print("📋 配置验证结果:")
        for config_type, is_valid in validation_results.items():
            status = "✅" if is_valid else "❌"
            print(f"  {status} {config_type}: {'有效' if is_valid else '无效'}")
        
        # 启动交互循环
        await interactive_loop_with_persistence_v2()

    except Exception as e:
        custom_error = error_handler.handle_error(e)
        print(f"\n💥 程序启动失败: {custom_error.message}")
        print(f"错误代码: {custom_error.error_code.value}")


if __name__ == "__main__":
    asyncio.run(main())
