"""
Web API接口实现
使用FastAPI提供REST API和WebSocket接口
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import os
import uuid
import mimetypes
from pathlib import Path

# 导入核心组件
from core.agent_core import agent_core
from services.config_manager import config_manager
from services.error_handler import error_handler, CustomError, ErrorCode
from services.file_manager import file_manager


# Pydantic模型定义
class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(..., description="消息角色: user, assistant, system")
    content: str = Field(..., description="消息内容")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now, description="时间戳")


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息")
    thread_id: Optional[str] = Field(None, description="会话ID")
    stream: bool = Field(False, description="是否流式响应")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    message: str = Field(..., description="AI回复")
    thread_id: str = Field(..., description="会话ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class SessionInfo(BaseModel):
    """会话信息模型"""
    thread_id: str = Field(..., description="会话ID")
    title: str = Field(..., description="会话标题")
    created_at: datetime = Field(..., description="创建时间")
    last_active_time: datetime = Field(..., description="最后活跃时间")
    message_count: int = Field(..., description="消息数量")


class CreateSessionRequest(BaseModel):
    """创建会话请求模型"""
    title: Optional[str] = Field(None, description="会话标题")


class UpdateSessionRequest(BaseModel):
    """更新会话请求模型"""
    title: str = Field(..., description="新的会话标题")


class FileUploadResponse(BaseModel):
    """文件上传响应模型"""
    file_id: str = Field(..., description="文件ID")
    filename: str = Field(..., description="原始文件名")
    file_path: str = Field(..., description="文件路径")
    file_size: int = Field(..., description="文件大小(字节)")
    file_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(default_factory=datetime.now, description="上传时间")


class FileInfo(BaseModel):
    """文件信息模型"""
    file_id: str = Field(..., description="文件ID")
    filename: str = Field(..., description="原始文件名")
    file_size: int = Field(..., description="文件大小(字节)")
    file_type: str = Field(..., description="文件类型")
    upload_time: datetime = Field(..., description="上传时间")
    download_url: str = Field(..., description="下载链接")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(..., description="版本信息")


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_threads: Dict[str, str] = {}  # connection_id -> thread_id
    
    async def connect(self, websocket: WebSocket, connection_id: str, thread_id: Optional[str] = None):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        if thread_id:
            self.connection_threads[connection_id] = thread_id
        print(f"WebSocket连接已建立: {connection_id}")
    
    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        if connection_id in self.connection_threads:
            del self.connection_threads[connection_id]
        print(f"WebSocket连接已断开: {connection_id}")
    
    async def send_message(self, connection_id: str, message: dict):
        """发送消息到指定连接"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                print(f"发送WebSocket消息失败: {e}")
                self.disconnect(connection_id)
    
    async def broadcast(self, message: dict):
        """广播消息到所有连接"""
        disconnected = []
        for connection_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                print(f"广播消息失败 {connection_id}: {e}")
                disconnected.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected:
            self.disconnect(connection_id)


# 文件上传配置
UPLOAD_DIR = Path("uploads")
UPLOAD_DIR.mkdir(exist_ok=True)
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
ALLOWED_EXTENSIONS = {
    # 图片
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg',
    # 文档
    '.pdf', '.doc', '.docx', '.txt', '.md', '.rtf',
    '.xls', '.xlsx', '.ppt', '.pptx',
    # 代码
    '.py', '.js', '.html', '.css', '.json', '.xml', '.yaml', '.yml',
    '.java', '.cpp', '.c', '.h', '.cs', '.php', '.rb', '.go', '.rs',
    # 压缩文件
    '.zip', '.rar', '.7z', '.tar', '.gz',
    # 音视频
    '.mp3', '.wav', '.mp4', '.avi', '.mov', '.mkv'
}

def get_file_type(filename: str) -> str:
    """获取文件类型"""
    ext = Path(filename).suffix.lower()
    mime_type, _ = mimetypes.guess_type(filename)

    if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']:
        return 'image'
    elif ext in ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf']:
        return 'document'
    elif ext in ['.xls', '.xlsx', '.ppt', '.pptx']:
        return 'spreadsheet'
    elif ext in ['.py', '.js', '.html', '.css', '.json', '.xml', '.yaml', '.yml', '.java', '.cpp', '.c', '.h', '.cs', '.php', '.rb', '.go', '.rs']:
        return 'code'
    elif ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
        return 'archive'
    elif ext in ['.mp3', '.wav']:
        return 'audio'
    elif ext in ['.mp4', '.avi', '.mov', '.mkv']:
        return 'video'
    else:
        return 'other'

def validate_file(file: UploadFile) -> tuple[bool, str]:
    """验证文件"""
    if not file.filename:
        return False, "文件名不能为空"

    # 检查文件扩展名
    ext = Path(file.filename).suffix.lower()
    if ext not in ALLOWED_EXTENSIONS:
        return False, f"不支持的文件类型: {ext}"

    # 检查文件大小
    if file.size and file.size > MAX_FILE_SIZE:
        return False, f"文件大小超过限制 ({MAX_FILE_SIZE // (1024*1024)}MB)"

    return True, ""

async def save_uploaded_file(file: UploadFile) -> tuple[str, str]:
    """保存上传的文件"""
    # 生成唯一文件ID
    file_id = str(uuid.uuid4())

    # 保持原始文件扩展名
    ext = Path(file.filename).suffix
    saved_filename = f"{file_id}{ext}"
    file_path = UPLOAD_DIR / saved_filename

    # 保存文件
    content = await file.read()
    with open(file_path, "wb") as f:
        f.write(content)

    return file_id, str(file_path)

# 创建FastAPI应用
def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    # 获取Web配置
    web_config = config_manager.get_web_config()
    
    app = FastAPI(
        title="LangGraph Agent Web API",
        description="基于LangGraph的智能助手Web API接口",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=web_config.get('cors_origins', ['*']),
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 配置静态文件服务
    app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")
    
    # 创建WebSocket管理器
    websocket_manager = WebSocketManager()
    
    # 健康检查端点
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """健康检查"""
        return HealthResponse(
            status="healthy",
            version="2.0.0"
        )
    
    # 聊天API端点
    @app.post("/api/chat", response_model=ChatResponse)
    async def chat_endpoint(request: ChatRequest):
        """聊天API端点"""
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            # 生成thread_id（如果未提供）
            thread_id = request.thread_id or f"web_user_{uuid.uuid4().hex[:8]}"
            
            # 处理消息
            response = await agent_core.process_message(
                message=request.message,
                thread_id=thread_id
            )

            return ChatResponse(
                message=response["response"],
                thread_id=thread_id
            )
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "chat", "request": request.dict()})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # 流式聊天API端点
    @app.post("/api/chat/stream")
    async def chat_stream_endpoint(request: ChatRequest):
        """流式聊天API端点"""
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            # 生成thread_id（如果未提供）
            thread_id = request.thread_id or f"web_user_{uuid.uuid4().hex[:8]}"
            
            async def generate():
                try:
                    async for chunk in agent_core.stream_message(
                        message=request.message,
                        thread_id=thread_id
                    ):
                        yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                except Exception as e:
                    error_data = {
                        "error": str(e),
                        "thread_id": thread_id,
                        "timestamp": datetime.now().isoformat()
                    }
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
            
            from fastapi.responses import StreamingResponse
            return StreamingResponse(
                generate(),
                media_type="text/plain",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream"
                }
            )
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "chat_stream", "request": request.dict()})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # 会话管理API
    @app.post("/api/sessions", response_model=SessionInfo)
    async def create_session(request: CreateSessionRequest):
        """创建新会话"""
        try:
            await agent_core.initialize()

            # 创建新会话
            thread_id = agent_core.create_session()

            return SessionInfo(
                thread_id=thread_id,
                title=request.title or "新对话",
                created_at=datetime.now(),
                last_active_time=datetime.now(),
                message_count=0
            )

        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "create_session", "request": request.dict()})
            raise HTTPException(status_code=500, detail=str(custom_error))

    @app.get("/api/sessions")
    async def list_sessions(user_id: Optional[str] = None):
        """获取用户的所有会话"""
        try:
            await agent_core.initialize()

            # 这里简化处理，实际应该从数据库获取
            # 目前返回当前用户的会话列表
            sessions = agent_core.list_user_sessions(user_id or "default_user")

            return {"sessions": sessions}

        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "list_sessions", "user_id": user_id})
            raise HTTPException(status_code=500, detail=str(custom_error))

    @app.get("/api/sessions/{thread_id}/history")
    async def get_session_history(thread_id: str):
        """获取会话历史"""
        try:
            await agent_core.initialize()

            history = await agent_core.get_session_history(thread_id)
            return {"thread_id": thread_id, "history": history}

        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "session_history", "thread_id": thread_id})
            raise HTTPException(status_code=500, detail=str(custom_error))

    @app.put("/api/sessions/{thread_id}")
    async def update_session(thread_id: str, request: UpdateSessionRequest):
        """更新会话信息"""
        try:
            await agent_core.initialize()

            # 这里简化处理，实际应该更新数据库
            return {"thread_id": thread_id, "title": request.title, "updated": True}

        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "update_session", "thread_id": thread_id})
            raise HTTPException(status_code=500, detail=str(custom_error))

    @app.delete("/api/sessions/{thread_id}")
    async def delete_session(thread_id: str):
        """删除会话"""
        try:
            await agent_core.initialize()

            # 这里简化处理，实际应该从数据库删除
            return {"thread_id": thread_id, "deleted": True}

        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "delete_session", "thread_id": thread_id})
            raise HTTPException(status_code=500, detail=str(custom_error))

    # 文件上传API
    @app.post("/api/upload", response_model=FileUploadResponse)
    async def upload_file(file: UploadFile = File(...), temporary: bool = False):
        """上传文件"""
        try:
            # 读取文件内容
            file_content = await file.read()

            # 使用文件管理器保存文件
            metadata = await file_manager.save_file(
                file_content=file_content,
                original_name=file.filename,
                temporary=temporary
            )

            return FileUploadResponse(
                file_id=metadata.file_id,
                filename=metadata.original_name,
                file_path=metadata.file_path,
                file_size=metadata.file_size,
                file_type=metadata.file_type
            )

        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "upload_file", "filename": file.filename})
            raise HTTPException(status_code=500, detail=str(custom_error))

    @app.post("/api/upload/multiple")
    async def upload_multiple_files(files: list[UploadFile] = File(...)):
        """批量上传文件"""
        try:
            if len(files) > 10:  # 限制最多10个文件
                raise HTTPException(status_code=400, detail="一次最多只能上传10个文件")

            results = []
            for file in files:
                # 验证文件
                is_valid, error_msg = validate_file(file)
                if not is_valid:
                    results.append({
                        "filename": file.filename,
                        "success": False,
                        "error": error_msg
                    })
                    continue

                try:
                    # 保存文件
                    file_id, file_path = await save_uploaded_file(file)
                    file_size = os.path.getsize(file_path)
                    file_type = get_file_type(file.filename)

                    results.append({
                        "filename": file.filename,
                        "success": True,
                        "file_id": file_id,
                        "file_path": file_path,
                        "file_size": file_size,
                        "file_type": file_type
                    })
                except Exception as e:
                    results.append({
                        "filename": file.filename,
                        "success": False,
                        "error": str(e)
                    })

            return {"results": results}

        except HTTPException:
            raise
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "upload_multiple_files"})
            raise HTTPException(status_code=500, detail=str(custom_error))

    @app.get("/api/files/{file_id}")
    async def get_file_info(file_id: str):
        """获取文件信息"""
        try:
            metadata = file_manager.get_file_metadata(file_id)
            if not metadata:
                raise HTTPException(status_code=404, detail="文件不存在")

            return FileInfo(
                file_id=metadata.file_id,
                filename=metadata.original_name,
                file_size=metadata.file_size,
                file_type=metadata.file_type,
                upload_time=metadata.upload_time,
                download_url=f"/uploads/{metadata.stored_name}"
            )

        except HTTPException:
            raise
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "get_file_info", "file_id": file_id})
            raise HTTPException(status_code=500, detail=str(custom_error))

    @app.delete("/api/files/{file_id}")
    async def delete_file(file_id: str):
        """删除文件"""
        try:
            success = file_manager.delete_file(file_id)
            if not success:
                raise HTTPException(status_code=404, detail="文件不存在")

            return {"file_id": file_id, "deleted": True}

        except HTTPException:
            raise
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "delete_file", "file_id": file_id})
            raise HTTPException(status_code=500, detail=str(custom_error))

    @app.get("/api/files")
    async def list_files(file_type: Optional[str] = None, limit: int = 100):
        """列出文件"""
        try:
            files = file_manager.list_files(file_type=file_type, limit=limit)
            return {
                "files": [
                    {
                        "file_id": f.file_id,
                        "filename": f.original_name,
                        "file_size": f.file_size,
                        "file_type": f.file_type,
                        "upload_time": f.upload_time,
                        "download_url": f"/uploads/{f.stored_name}"
                    }
                    for f in files
                ]
            }

        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "list_files"})
            raise HTTPException(status_code=500, detail=str(custom_error))

    @app.get("/api/storage/stats")
    async def get_storage_stats():
        """获取存储统计信息"""
        try:
            stats = file_manager.get_storage_stats()
            return stats

        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "storage_stats"})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # 获取可用工具列表
    @app.get("/api/tools")
    async def get_available_tools():
        """获取可用工具列表"""
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            tools = agent_core.get_available_tools()
            return {"tools": tools}
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "tools"})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # WebSocket端点
    @app.websocket("/ws/{thread_id}")
    async def websocket_endpoint(websocket: WebSocket, thread_id: str):
        """WebSocket聊天端点"""
        connection_id = f"ws_{uuid.uuid4().hex[:8]}"
        
        try:
            await websocket_manager.connect(websocket, connection_id, thread_id)
            
            # 确保agent已初始化
            await agent_core.initialize()
            
            # 发送连接确认
            await websocket_manager.send_message(connection_id, {
                "type": "connection",
                "status": "connected",
                "thread_id": thread_id,
                "connection_id": connection_id
            })
            
            while True:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                if message_data.get("type") == "chat":
                    user_message = message_data.get("message", "")
                    
                    # 发送用户消息确认
                    await websocket_manager.send_message(connection_id, {
                        "type": "user_message",
                        "message": user_message,
                        "timestamp": datetime.now().isoformat()
                    })
                    
                    # 流式处理并发送AI回复
                    async for chunk in agent_core.stream_message(user_message, thread_id):
                        await websocket_manager.send_message(connection_id, {
                            "type": "ai_chunk",
                            "chunk": chunk,
                            "timestamp": datetime.now().isoformat()
                        })
                    
                    # 发送完成信号
                    await websocket_manager.send_message(connection_id, {
                        "type": "ai_complete",
                        "timestamp": datetime.now().isoformat()
                    })
                
        except WebSocketDisconnect:
            websocket_manager.disconnect(connection_id)
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "websocket", "connection_id": connection_id})
            await websocket_manager.send_message(connection_id, {
                "type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            websocket_manager.disconnect(connection_id)
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    # 获取Web配置
    web_config = config_manager.get_web_config()
    
    uvicorn.run(
        "interfaces.web_api:app",
        host=web_config.get('host', '0.0.0.0'),
        port=web_config.get('port', 8000),
        reload=web_config.get('debug', False),
        log_level="info"
    )
