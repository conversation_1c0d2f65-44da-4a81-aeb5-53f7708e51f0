# llm_loader.py
import json
from langchain_openai import ChatOpenAI

def load_llm_from_config(config_path: str = "llm_config.json"):
    """
    从指定的JSON配置文件加载并初始化一个OpenAI兼容的LLM。
    自动使用配置文件中指定的默认提供商。

    Args:
        config_path (str): LLM配置文件的路径。

    Returns:
        ChatOpenAI: 配置好的LangChain LLM实例。
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print(f"错误: 配置文件 '{config_path}' 未找到。")
        exit(1)
    except json.JSONDecodeError:
        print(f"错误: 无法解析配置文件 '{config_path}'。请检查其JSON格式。")
        exit(1)

    print(f"正在从 '{config_path}' 加载LLM配置...")

    # 检查是否为新的多模型配置格式
    if "models" in config:
        return _load_from_multi_model_config(config)
    else:
        # 兼容旧的单模型配置格式
        print("⚠️ 检测到旧的配置格式，建议升级到多模型配置格式")
        return _load_from_legacy_config(config)

def _load_from_multi_model_config(config: dict):
    """从新的多模型配置格式加载LLM"""
    models = config.get("models", {})
    default_provider = config.get("default_provider")

    # 使用配置文件中指定的默认提供商
    target_provider = default_provider

    if not target_provider:
        raise ValueError("未指定提供商且配置文件中没有默认提供商")

    if target_provider not in models:
        available_providers = list(models.keys())
        raise ValueError(f"提供商 '{target_provider}' 不存在。可用提供商: {available_providers}")

    model_config = models[target_provider]

    # 从配置中提取参数
    api_key = model_config.get("api_key")
    base_url = model_config.get("base_url")
    model_name = model_config.get("model_name")
    description = model_config.get("description", "")

    print(f"  - 提供商: {target_provider}")
    print(f"  - 模型名称: {model_name}")
    print(f"  - API Base URL: {base_url}")
    print(f"  - 描述: {description}")

    # 创建并返回LLM实例
    llm = ChatOpenAI(
        api_key=api_key,
        base_url=base_url,
        model=model_name,
        temperature=0,
        streaming=True
    )

    return llm

def _load_from_legacy_config(config: dict):
    """从旧的单模型配置格式加载LLM"""
    print(f"  - 模型名称: {config.get('model_name')}")
    print(f"  - API Base URL: {config.get('api_base')}")
    print(f"  - API Key: {'***' if config.get('api_key') else 'None'}")

    # ChatOpenAI 能够智能处理 base_url 为 None 或空字符串的情况
    llm = ChatOpenAI(
        model=config.get("model_name"),
        api_key=config.get("api_key"),
        base_url=config.get("api_base"),  # 修复字段名
        temperature=0,
        streaming=True
    )

    return llm

def list_available_models(config_path: str = "llm_config.json"):
    """
    列出配置文件中所有可用的模型。

    Args:
        config_path (str): LLM配置文件的路径。

    Returns:
        dict: 包含所有模型配置的字典。
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except FileNotFoundError:
        print(f"错误: 配置文件 '{config_path}' 未找到。")
        return {}
    except json.JSONDecodeError:
        print(f"错误: 无法解析配置文件 '{config_path}'。")
        return {}

    if "models" not in config:
        print("⚠️ 配置文件使用旧格式，不支持多模型列表")
        return {}

    models = config.get("models", {})
    default_provider = config.get("default_provider")

    print("📋 可用模型列表:")
    for provider, model_config in models.items():
        default_mark = " (默认)" if provider == default_provider else ""
        description = model_config.get("description", "")

        print(f"  - {provider}{default_mark}")
        print(f"    模型: {model_config.get('model_name', 'N/A')}")
        print(f"    描述: {description}")
        print(f"    URL: {model_config.get('base_url', 'N/A')}")
        print()

    return models