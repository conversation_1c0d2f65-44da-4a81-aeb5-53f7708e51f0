<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LangGraph Agent Web API 测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .chat-container {
            display: flex;
            gap: 20px;
        }
        .chat-section {
            flex: 1;
        }
        .chat-messages {
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            overflow-y: auto;
            background: #fafafa;
            margin-bottom: 10px;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 8px;
            max-width: 80%;
        }
        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .ai-message {
            background: #e9ecef;
            color: #333;
        }
        .system-message {
            background: #ffc107;
            color: #333;
            font-style: italic;
            text-align: center;
            max-width: 100%;
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }
        input[type="text"], textarea {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .tools-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: #fafafa;
        }
        .tool-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .tool-item:last-child {
            border-bottom: none;
        }
        .config-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .config-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        .config-item label {
            min-width: 120px;
            font-weight: bold;
        }
        .config-item input {
            flex: 1;
        }
    </style>
</head>
<body>
    <h1>🤖 LangGraph Agent Web API 测试界面</h1>
    
    <!-- 配置区域 -->
    <div class="container">
        <h2>⚙️ 配置</h2>
        <div class="config-section">
            <div>
                <div class="config-item">
                    <label>API地址:</label>
                    <input type="text" id="apiUrl" value="http://localhost:8000">
                </div>
                <div class="config-item">
                    <label>WebSocket地址:</label>
                    <input type="text" id="wsUrl" value="ws://localhost:8000">
                </div>
                <div class="config-item">
                    <label>会话ID:</label>
                    <input type="text" id="threadId" value="">
                    <button onclick="generateThreadId()">生成新ID</button>
                </div>
            </div>
            <div>
                <button onclick="checkHealth()">健康检查</button>
                <button onclick="loadTools()">加载工具列表</button>
                <button onclick="clearMessages()">清空消息</button>
            </div>
        </div>
    </div>

    <!-- 聊天区域 -->
    <div class="container">
        <h2>💬 聊天测试</h2>
        <div class="chat-container">
            <!-- REST API 聊天 -->
            <div class="chat-section">
                <h3>REST API 聊天</h3>
                <div class="chat-messages" id="restMessages"></div>
                <div class="input-group">
                    <textarea id="restInput" placeholder="输入消息..." rows="2"></textarea>
                    <div>
                        <button onclick="sendRestMessage()">发送</button>
                        <button onclick="sendStreamMessage()">流式发送</button>
                    </div>
                </div>
            </div>

            <!-- WebSocket 聊天 -->
            <div class="chat-section">
                <h3>WebSocket 聊天</h3>
                <div class="status disconnected" id="wsStatus">未连接</div>
                <div class="chat-messages" id="wsMessages"></div>
                <div class="input-group">
                    <textarea id="wsInput" placeholder="输入消息..." rows="2"></textarea>
                    <div>
                        <button onclick="connectWebSocket()">连接</button>
                        <button onclick="sendWebSocketMessage()">发送</button>
                        <button onclick="disconnectWebSocket()">断开</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工具列表 -->
    <div class="container">
        <h2>🔧 可用工具</h2>
        <div class="tools-list" id="toolsList">
            <p>点击"加载工具列表"按钮加载可用工具</p>
        </div>
    </div>

    <script>
        let websocket = null;
        let currentThreadId = '';

        // 生成新的会话ID
        function generateThreadId() {
            const threadId = 'web_test_' + Math.random().toString(36).substr(2, 9);
            document.getElementById('threadId').value = threadId;
            currentThreadId = threadId;
        }

        // 初始化
        window.onload = function() {
            generateThreadId();
        };

        // 添加消息到聊天区域
        function addMessage(containerId, message, type = 'system') {
            const container = document.getElementById(containerId);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.innerHTML = message;
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 清空消息
        function clearMessages() {
            document.getElementById('restMessages').innerHTML = '';
            document.getElementById('wsMessages').innerHTML = '';
        }

        // 健康检查
        async function checkHealth() {
            const apiUrl = document.getElementById('apiUrl').value;
            try {
                const response = await fetch(`${apiUrl}/health`);
                const data = await response.json();
                addMessage('restMessages', `健康检查: ${data.status} (版本: ${data.version})`, 'system');
            } catch (error) {
                addMessage('restMessages', `健康检查失败: ${error.message}`, 'system');
            }
        }

        // 加载工具列表
        async function loadTools() {
            const apiUrl = document.getElementById('apiUrl').value;
            try {
                const response = await fetch(`${apiUrl}/api/tools`);
                const data = await response.json();
                const toolsList = document.getElementById('toolsList');
                toolsList.innerHTML = '';
                
                if (data.tools && data.tools.length > 0) {
                    data.tools.forEach(tool => {
                        const toolDiv = document.createElement('div');
                        toolDiv.className = 'tool-item';
                        toolDiv.innerHTML = `<strong>${tool.name}</strong>: ${tool.description}`;
                        toolsList.appendChild(toolDiv);
                    });
                } else {
                    toolsList.innerHTML = '<p>没有可用工具</p>';
                }
            } catch (error) {
                document.getElementById('toolsList').innerHTML = `<p>加载工具失败: ${error.message}</p>`;
            }
        }

        // 发送REST消息
        async function sendRestMessage() {
            const apiUrl = document.getElementById('apiUrl').value;
            const message = document.getElementById('restInput').value.trim();
            const threadId = document.getElementById('threadId').value || currentThreadId;
            
            if (!message) return;
            
            addMessage('restMessages', message, 'user');
            document.getElementById('restInput').value = '';
            
            try {
                const response = await fetch(`${apiUrl}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        thread_id: threadId
                    })
                });
                
                const data = await response.json();
                addMessage('restMessages', data.message, 'ai');
            } catch (error) {
                addMessage('restMessages', `错误: ${error.message}`, 'system');
            }
        }

        // 发送流式消息
        async function sendStreamMessage() {
            const apiUrl = document.getElementById('apiUrl').value;
            const message = document.getElementById('restInput').value.trim();
            const threadId = document.getElementById('threadId').value || currentThreadId;
            
            if (!message) return;
            
            addMessage('restMessages', message, 'user');
            document.getElementById('restInput').value = '';
            
            try {
                const response = await fetch(`${apiUrl}/api/chat/stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        thread_id: threadId,
                        stream: true
                    })
                });
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let aiMessage = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                if (data.chunk) {
                                    aiMessage += data.chunk;
                                }
                            } catch (e) {
                                // 忽略解析错误
                            }
                        }
                    }
                }
                
                if (aiMessage) {
                    addMessage('restMessages', aiMessage, 'ai');
                }
            } catch (error) {
                addMessage('restMessages', `流式请求错误: ${error.message}`, 'system');
            }
        }

        // 连接WebSocket
        function connectWebSocket() {
            const wsUrl = document.getElementById('wsUrl').value;
            const threadId = document.getElementById('threadId').value || currentThreadId;
            
            if (websocket) {
                websocket.close();
            }
            
            websocket = new WebSocket(`${wsUrl}/ws/${threadId}`);
            
            websocket.onopen = function(event) {
                document.getElementById('wsStatus').textContent = '已连接';
                document.getElementById('wsStatus').className = 'status connected';
                addMessage('wsMessages', 'WebSocket连接已建立', 'system');
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                
                if (data.type === 'ai_chunk') {
                    // 处理流式响应块
                    const lastMessage = document.querySelector('#wsMessages .message:last-child');
                    if (lastMessage && lastMessage.classList.contains('ai-message')) {
                        lastMessage.innerHTML += data.chunk;
                    } else {
                        addMessage('wsMessages', data.chunk, 'ai');
                    }
                } else if (data.type === 'ai_complete') {
                    // AI回复完成
                    addMessage('wsMessages', '--- 回复完成 ---', 'system');
                } else if (data.type === 'error') {
                    addMessage('wsMessages', `错误: ${data.error}`, 'system');
                } else {
                    addMessage('wsMessages', JSON.stringify(data), 'system');
                }
            };
            
            websocket.onclose = function(event) {
                document.getElementById('wsStatus').textContent = '已断开';
                document.getElementById('wsStatus').className = 'status disconnected';
                addMessage('wsMessages', 'WebSocket连接已断开', 'system');
            };
            
            websocket.onerror = function(error) {
                addMessage('wsMessages', `WebSocket错误: ${error}`, 'system');
            };
        }

        // 发送WebSocket消息
        function sendWebSocketMessage() {
            const message = document.getElementById('wsInput').value.trim();
            
            if (!message || !websocket || websocket.readyState !== WebSocket.OPEN) {
                return;
            }
            
            addMessage('wsMessages', message, 'user');
            document.getElementById('wsInput').value = '';
            
            websocket.send(JSON.stringify({
                type: 'chat',
                message: message
            }));
        }

        // 断开WebSocket
        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        // 回车发送消息
        document.getElementById('restInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendRestMessage();
            }
        });

        document.getElementById('wsInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendWebSocketMessage();
            }
        });
    </script>
</body>
</html>
