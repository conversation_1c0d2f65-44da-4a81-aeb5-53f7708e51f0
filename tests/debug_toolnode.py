#!/usr/bin/env python3
"""
调试ToolNode的可调用性问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_toolnode():
    """调试ToolNode的可调用性"""
    print("🔍 调试ToolNode可调用性问题")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from langgraph.prebuilt import ToolNode
        from langchain_core.tools import tool
        from langchain_core.messages import AIMessage
        
        # 创建一个简单的工具
        @tool
        def test_tool(query: str) -> str:
            """测试工具"""
            return f"测试结果: {query}"
        
        # 创建ToolNode
        tool_node = ToolNode([test_tool])
        
        print(f"✅ ToolNode创建成功: {type(tool_node)}")
        print(f"📋 ToolNode类型: {tool_node.__class__}")
        print(f"📋 ToolNode模块: {tool_node.__class__.__module__}")
        
        # 检查ToolNode的属性和方法
        print("\n🔍 ToolNode属性和方法:")
        for attr in dir(tool_node):
            if not attr.startswith('_'):
                print(f"  - {attr}: {type(getattr(tool_node, attr))}")
        
        # 检查是否有__call__方法
        has_call = hasattr(tool_node, '__call__')
        print(f"\n📋 ToolNode有__call__方法: {has_call}")
        
        # 检查是否可调用
        is_callable = callable(tool_node)
        print(f"📋 ToolNode是否可调用: {is_callable}")
        
        # 检查invoke方法
        has_invoke = hasattr(tool_node, 'invoke')
        print(f"📋 ToolNode有invoke方法: {has_invoke}")
        
        if has_invoke:
            invoke_callable = callable(getattr(tool_node, 'invoke'))
            print(f"📋 invoke方法是否可调用: {invoke_callable}")
        
        # 尝试调用invoke方法
        print("\n🧪 测试invoke方法:")
        test_message = AIMessage(
            content="",
            tool_calls=[{
                "name": "test_tool",
                "args": {"query": "测试"},
                "id": "test_id",
                "type": "tool_call"
            }]
        )
        
        try:
            result = tool_node.invoke({"messages": [test_message]})
            print(f"✅ invoke方法调用成功: {result}")
        except Exception as e:
            print(f"❌ invoke方法调用失败: {e}")
        
        # 尝试直接调用ToolNode
        print("\n🧪 测试直接调用ToolNode:")
        try:
            if is_callable:
                result = tool_node({"messages": [test_message]})
                print(f"✅ 直接调用成功: {result}")
            else:
                print("❌ ToolNode不可直接调用")
        except Exception as e:
            print(f"❌ 直接调用失败: {e}")
        
        # 检查ToolNode的基类
        print(f"\n📋 ToolNode基类: {tool_node.__class__.__bases__}")
        
        # 检查是否是Runnable
        try:
            from langchain_core.runnables import Runnable
            is_runnable = isinstance(tool_node, Runnable)
            print(f"📋 ToolNode是否是Runnable: {is_runnable}")
        except ImportError:
            print("📋 无法导入Runnable")
        
        return is_callable
        
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_toolnode()
