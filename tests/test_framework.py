"""
综合测试框架
提供统一的测试基础设施和工具
"""
import pytest
import asyncio
import time
import psutil
import threading
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
import json
import logging

# 配置测试日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestFramework:
    """综合测试框架类"""
    
    def __init__(self):
        self.test_results: Dict[str, Any] = {}
        self.performance_metrics: Dict[str, float] = {}
        self.start_time: Optional[float] = None
        
    def start_test_suite(self, suite_name: str):
        """开始测试套件"""
        self.start_time = time.time()
        logger.info(f"🚀 开始测试套件: {suite_name}")
        
    def end_test_suite(self, suite_name: str):
        """结束测试套件"""
        if self.start_time:
            duration = time.time() - self.start_time
            logger.info(f"✅ 测试套件 {suite_name} 完成，耗时: {duration:.2f}秒")
            
    def measure_performance(self, func: Callable, *args, **kwargs) -> Dict[str, Any]:
        """测量函数性能"""
        process = psutil.Process()
        
        # 记录开始状态
        start_time = time.time()
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        start_cpu = process.cpu_percent()
        
        # 执行函数
        try:
            if asyncio.iscoroutinefunction(func):
                result = asyncio.run(func(*args, **kwargs))
            else:
                result = func(*args, **kwargs)
            success = True
            error = None
        except Exception as e:
            result = None
            success = False
            error = str(e)
        
        # 记录结束状态
        end_time = time.time()
        end_memory = process.memory_info().rss / 1024 / 1024  # MB
        end_cpu = process.cpu_percent()
        
        metrics = {
            'success': success,
            'result': result,
            'error': error,
            'duration': end_time - start_time,
            'memory_usage': end_memory - start_memory,
            'cpu_usage': end_cpu - start_cpu,
            'peak_memory': end_memory
        }
        
        return metrics
    
    async def stress_test(self, func: Callable, iterations: int = 100, 
                         concurrent: int = 10) -> Dict[str, Any]:
        """压力测试"""
        logger.info(f"🔥 开始压力测试: {iterations}次迭代, {concurrent}并发")
        
        results = []
        errors = []
        
        async def run_iteration():
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func()
                else:
                    result = func()
                results.append(result)
                return True
            except Exception as e:
                errors.append(str(e))
                return False
        
        # 并发执行
        tasks = []
        for i in range(iterations):
            if len(tasks) >= concurrent:
                # 等待一批任务完成
                await asyncio.gather(*tasks[:concurrent])
                tasks = tasks[concurrent:]
            
            tasks.append(run_iteration())
        
        # 等待剩余任务
        if tasks:
            await asyncio.gather(*tasks)
        
        success_rate = len(results) / iterations * 100
        
        return {
            'total_iterations': iterations,
            'successful': len(results),
            'failed': len(errors),
            'success_rate': success_rate,
            'errors': errors[:10]  # 只保留前10个错误
        }
    
    def memory_leak_test(self, func: Callable, iterations: int = 50) -> Dict[str, Any]:
        """内存泄漏测试"""
        logger.info(f"🧠 开始内存泄漏测试: {iterations}次迭代")
        
        process = psutil.Process()
        memory_samples = []
        
        for i in range(iterations):
            # 执行函数
            try:
                if asyncio.iscoroutinefunction(func):
                    asyncio.run(func())
                else:
                    func()
            except Exception as e:
                logger.warning(f"迭代 {i} 失败: {e}")
            
            # 记录内存使用
            memory_mb = process.memory_info().rss / 1024 / 1024
            memory_samples.append(memory_mb)
            
            # 每10次迭代强制垃圾回收
            if i % 10 == 0:
                import gc
                gc.collect()
        
        # 分析内存趋势
        if len(memory_samples) >= 10:
            start_avg = sum(memory_samples[:5]) / 5
            end_avg = sum(memory_samples[-5:]) / 5
            memory_growth = end_avg - start_avg
            
            # 判断是否有内存泄漏（增长超过10MB认为可能有泄漏）
            has_leak = memory_growth > 10
        else:
            memory_growth = 0
            has_leak = False
        
        return {
            'iterations': iterations,
            'memory_samples': memory_samples,
            'memory_growth_mb': memory_growth,
            'has_potential_leak': has_leak,
            'peak_memory_mb': max(memory_samples),
            'avg_memory_mb': sum(memory_samples) / len(memory_samples)
        }
    
    def save_test_report(self, report_path: str):
        """保存测试报告"""
        report = {
            'test_results': self.test_results,
            'performance_metrics': self.performance_metrics,
            'timestamp': time.time(),
            'summary': self._generate_summary()
        }
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 测试报告已保存: {report_path}")
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成测试摘要"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if result.get('success', False))
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': passed_tests / total_tests * 100 if total_tests > 0 else 0
        }

# 全局测试框架实例
test_framework = TestFramework()

# 测试装饰器
def performance_test(name: str):
    """性能测试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            metrics = test_framework.measure_performance(func, *args, **kwargs)
            test_framework.test_results[name] = metrics
            test_framework.performance_metrics[name] = metrics['duration']
            return metrics['result']
        return wrapper
    return decorator

def stress_test_decorator(name: str, iterations: int = 100, concurrent: int = 10):
    """压力测试装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 创建测试函数
            async def test_func():
                return await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            
            metrics = await test_framework.stress_test(test_func, iterations, concurrent)
            test_framework.test_results[name] = metrics
            return metrics
        return wrapper
    return decorator

# pytest fixtures
@pytest.fixture
def framework():
    """测试框架fixture"""
    return test_framework

@pytest.fixture
def performance_monitor():
    """性能监控fixture"""
    def monitor(func, *args, **kwargs):
        return test_framework.measure_performance(func, *args, **kwargs)
    return monitor
