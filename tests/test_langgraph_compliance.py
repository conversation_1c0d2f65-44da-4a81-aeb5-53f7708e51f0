#!/usr/bin/env python3
"""
LangGraph官方标准合规性验证测试
严格验证所有LangGraph相关实现是否遵循官方标准
"""

import asyncio
import sys
import os
import inspect
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

async def test_langgraph_imports():
    """测试LangGraph官方导入"""
    try:
        print('🔍 验证LangGraph官方导入...')
        
        # 验证核心LangGraph导入
        from langgraph.graph import StateGraph
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        from langgraph.prebuilt import ToolNode
        from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
        
        print('✅ LangGraph核心模块导入成功')
        print(f'  - StateGraph: {StateGraph.__module__}')
        print(f'  - AsyncSqliteSaver: {AsyncSqliteSaver.__module__}')
        print(f'  - ToolNode: {ToolNode.__module__}')
        
        return True
        
    except ImportError as e:
        print(f'❌ LangGraph导入失败: {e}')
        return False

async def test_checkpointer_compliance():
    """测试检查点存储器合规性"""
    try:
        print('\n🔍 验证AsyncSqliteSaver合规性...')
        
        import main
        
        # 测试检查点存储器创建
        checkpointer = await main.create_checkpointer()
        
        # 验证是否为AsyncSqliteSaver实例
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        if not isinstance(checkpointer, AsyncSqliteSaver):
            print(f'❌ 检查点存储器类型错误: {type(checkpointer)}')
            return False
        
        print('✅ AsyncSqliteSaver实例验证通过')
        
        # 验证必要的方法存在
        required_methods = ['aput', 'aget', 'alist', 'aput_writes']
        for method in required_methods:
            if not hasattr(checkpointer, method):
                print(f'❌ 缺少必要方法: {method}')
                return False
            print(f'✅ 方法存在: {method}')
        
        return True
        
    except Exception as e:
        print(f'❌ 检查点存储器测试失败: {e}')
        return False

async def test_state_graph_compliance():
    """测试StateGraph合规性"""
    try:
        print('\n🔍 验证StateGraph实现合规性...')
        
        import main
        
        # 初始化Agent获取StateGraph
        app, tools, session_manager = await main.initialize_agent()
        
        # 验证app是否为CompiledGraph实例
        from langgraph.graph.graph import CompiledGraph
        if not isinstance(app, CompiledGraph):
            print(f'❌ App类型错误: {type(app)}')
            return False
        
        print('✅ CompiledGraph实例验证通过')
        
        # 验证必要的异步方法
        required_async_methods = ['ainvoke', 'astream', 'aget_state', 'aupdate_state']
        for method in required_async_methods:
            if not hasattr(app, method):
                print(f'❌ 缺少异步方法: {method}')
                return False
            # 对于astream，它返回异步生成器，不是直接的协程函数
            if method == 'astream':
                # 检查astream是否可调用
                if not callable(getattr(app, method)):
                    print(f'❌ 方法不可调用: {method}')
                    return False
            else:
                # 其他方法应该是协程函数
                if not asyncio.iscoroutinefunction(getattr(app, method)):
                    print(f'❌ 方法不是协程: {method}')
                    return False
            print(f'✅ 异步方法存在: {method}')
        
        # 验证检查点集成
        if not hasattr(app, 'checkpointer') or app.checkpointer is None:
            print('❌ StateGraph未正确集成检查点存储器')
            return False
        
        print('✅ StateGraph检查点集成验证通过')
        
        return True
        
    except Exception as e:
        print(f'❌ StateGraph测试失败: {e}')
        return False

async def test_tool_node_compliance():
    """测试ToolNode合规性"""
    try:
        print('\n🔍 验证ToolNode实现合规性...')
        
        import main
        
        # 初始化Agent获取工具
        app, tools, session_manager = await main.initialize_agent()
        
        # 验证工具列表
        if not tools or len(tools) == 0:
            print('❌ 工具列表为空')
            return False
        
        print(f'✅ 工具数量: {len(tools)}')
        
        # 验证工具结构
        for i, tool in enumerate(tools[:3]):  # 检查前3个工具
            if not hasattr(tool, 'name'):
                print(f'❌ 工具{i}缺少name属性')
                return False
            if not hasattr(tool, 'description'):
                print(f'❌ 工具{i}缺少description属性')
                return False
            print(f'✅ 工具{i}: {tool.name}')
        
        # 验证ToolNode可以正确创建
        from langgraph.prebuilt import ToolNode
        tool_node = ToolNode(tools)

        # ToolNode应该有invoke方法
        if not hasattr(tool_node, 'invoke'):
            print('❌ ToolNode缺少invoke方法')
            return False

        # 验证invoke方法是可调用的
        if not callable(getattr(tool_node, 'invoke')):
            print('❌ ToolNode.invoke不可调用')
            return False

        # 根据LangGraph官方文档，ToolNode是Runnable，不需要直接callable
        # 检查ToolNode是否是Runnable类型
        from langgraph.utils.runnable import RunnableCallable
        if not isinstance(tool_node, RunnableCallable):
            print('❌ ToolNode不是RunnableCallable类型')
            return False

        print('✅ ToolNode创建和验证通过 (符合LangGraph官方标准)')
        
        return True
        
    except Exception as e:
        print(f'❌ ToolNode测试失败: {e}')
        return False

async def test_message_state_compliance():
    """测试消息状态合规性"""
    try:
        print('\n🔍 验证消息状态合规性...')
        
        import main
        
        # 初始化Agent
        app, tools, session_manager = await main.initialize_agent()
        
        # 创建测试会话
        thread_id = session_manager.create_session('compliance_test')
        config = session_manager.get_session_config(thread_id)
        
        # 获取初始状态
        state = await app.aget_state(config)
        
        # 验证状态结构
        if not hasattr(state, 'values'):
            print('❌ 状态缺少values属性')
            return False
        
        print('✅ 状态结构验证通过')
        
        # 测试消息添加
        from langchain_core.messages import HumanMessage
        test_message = HumanMessage(content="测试消息")
        
        await app.aupdate_state(config, {"messages": [test_message]})
        updated_state = await app.aget_state(config)
        
        if "messages" not in updated_state.values:
            print('❌ 消息状态更新失败')
            return False
        
        messages = updated_state.values["messages"]
        if len(messages) == 0:
            print('❌ 消息列表为空')
            return False
        
        # 验证消息类型
        from langchain_core.messages import BaseMessage
        if not isinstance(messages[0], BaseMessage):
            print(f'❌ 消息类型错误: {type(messages[0])}')
            return False
        
        print('✅ 消息状态和类型验证通过')
        
        return True
        
    except Exception as e:
        print(f'❌ 消息状态测试失败: {e}')
        return False

async def test_persistence_compliance():
    """测试持久化合规性"""
    try:
        print('\n🔍 验证持久化机制合规性...')
        
        import main
        
        # 初始化Agent
        app, tools, session_manager = await main.initialize_agent()
        
        # 创建测试会话
        thread_id = session_manager.create_session('persistence_test')
        config = session_manager.get_session_config(thread_id)
        
        # 验证配置结构
        if 'configurable' not in config:
            print('❌ 配置缺少configurable字段')
            return False
        
        configurable = config['configurable']
        if 'thread_id' not in configurable:
            print('❌ 配置缺少thread_id')
            return False
        
        print(f'✅ 配置结构正确: thread_id={configurable["thread_id"]}')
        
        # 测试状态持久化
        from langchain_core.messages import HumanMessage
        test_message = HumanMessage(content="持久化测试")
        
        # 添加消息并运行
        await app.aupdate_state(config, {"messages": [test_message]})
        await app.ainvoke(None, config)
        
        # 验证状态被持久化
        persisted_state = await app.aget_state(config)
        if "messages" not in persisted_state.values:
            print('❌ 状态未被持久化')
            return False
        
        persisted_messages = persisted_state.values["messages"]
        if len(persisted_messages) < 2:  # 至少包含用户消息和AI回复
            print('❌ 持久化消息数量不正确')
            return False
        
        print(f'✅ 持久化验证通过，消息数: {len(persisted_messages)}')
        
        return True
        
    except Exception as e:
        print(f'❌ 持久化测试失败: {e}')
        return False

async def main():
    """主测试函数"""
    print('🚀 开始LangGraph官方标准合规性验证')
    print('严格验证所有实现是否遵循LangGraph官方标准')
    print('='*70)
    
    # 运行所有合规性测试
    tests = [
        ("LangGraph导入", test_langgraph_imports),
        ("检查点存储器", test_checkpointer_compliance),
        ("StateGraph实现", test_state_graph_compliance),
        ("ToolNode实现", test_tool_node_compliance),
        ("消息状态", test_message_state_compliance),
        ("持久化机制", test_persistence_compliance),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f'\n📋 执行测试: {test_name}')
        try:
            result = await test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f'📊 {test_name}: {status}')
        except Exception as e:
            print(f'💥 {test_name}: 异常 - {e}')
            results.append((test_name, False))
    
    # 总结测试结果
    print('\n' + '='*70)
    print('📊 LangGraph官方标准合规性验证结果:')
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f'  {status} {test_name}')
        if result:
            passed += 1
    
    print(f'\n📈 总体结果: {passed}/{total} 测试通过')
    
    if passed == total:
        print('\n🎉 所有LangGraph官方标准合规性验证通过！')
        print('✅ 严格遵循LangGraph官方标准')
        print('✅ AsyncSqliteSaver正确实现')
        print('✅ StateGraph正确集成')
        print('✅ ToolNode正确使用')
        print('✅ 消息状态正确处理')
        print('✅ 持久化机制正确工作')
        return 0
    else:
        print(f'\n⚠️ {total - passed} 项测试失败，需要修复')
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
