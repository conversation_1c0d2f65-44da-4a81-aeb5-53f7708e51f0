<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式响应测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        button:hover {
            background: #f0f0f0;
            border-color: #1890ff;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .primary-btn {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .primary-btn:hover {
            background: #40a9ff;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .test-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.warning { background: #fff3cd; color: #856404; }
        .typewriter-demo {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background: white;
            margin-bottom: 10px;
        }
        .cursor {
            animation: blink 1s infinite;
            color: #1890ff;
            font-weight: bold;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
    </style>
</head>
<body>
    <h1>🚀 前端流式响应功能测试</h1>
    
    <!-- SSE连接测试 -->
    <div class="test-container">
        <h2 class="test-title">1. SSE连接测试</h2>
        <div class="test-controls">
            <input type="text" id="sseMessage" class="test-input" placeholder="输入测试消息..." value="你好，请介绍一下自己">
            <button onclick="testSSE()" class="primary-btn">测试SSE连接</button>
            <button onclick="clearSSEOutput()">清空输出</button>
        </div>
        <div id="sseStatus" class="status info">准备测试SSE连接</div>
        <div id="sseOutput" class="test-output">等待测试结果...</div>
    </div>

    <!-- 打字机效果测试 -->
    <div class="test-container">
        <h2 class="test-title">2. 打字机效果测试</h2>
        <div class="test-controls">
            <input type="text" id="typewriterText" class="test-input" placeholder="输入要显示的文本..." value="这是一个打字机效果的演示，每个字符会逐个显示出来。">
            <input type="number" id="typewriterSpeed" class="test-input" placeholder="速度(毫秒)" value="50" style="width: 120px;">
            <button onclick="testTypewriter()" class="primary-btn">开始打字机效果</button>
            <button onclick="stopTypewriter()">停止</button>
        </div>
        <div class="typewriter-demo">
            <span id="typewriterDisplay"></span><span id="typewriterCursor" class="cursor">|</span>
        </div>
    </div>

    <!-- API接口测试 -->
    <div class="test-container">
        <h2 class="test-title">3. API接口测试</h2>
        <div class="test-controls">
            <button onclick="testHealthCheck()" class="primary-btn">健康检查</button>
            <button onclick="testGetTools()">获取工具列表</button>
            <button onclick="testRegularChat()">常规聊天</button>
            <button onclick="clearApiOutput()">清空输出</button>
        </div>
        <div id="apiStatus" class="status info">准备测试API接口</div>
        <div id="apiOutput" class="test-output">等待测试结果...</div>
    </div>

    <!-- 性能测试 -->
    <div class="test-container">
        <h2 class="test-title">4. 性能测试</h2>
        <div class="test-controls">
            <button onclick="testPerformance()" class="primary-btn">开始性能测试</button>
            <button onclick="clearPerfOutput()">清空输出</button>
        </div>
        <div id="perfStatus" class="status info">准备进行性能测试</div>
        <div id="perfOutput" class="test-output">等待测试结果...</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let typewriterTimer = null;
        let sseEventSource = null;

        // SSE连接测试
        async function testSSE() {
            const message = document.getElementById('sseMessage').value;
            const statusEl = document.getElementById('sseStatus');
            const outputEl = document.getElementById('sseOutput');
            
            if (!message.trim()) {
                statusEl.className = 'status error';
                statusEl.textContent = '请输入测试消息';
                return;
            }

            statusEl.className = 'status info';
            statusEl.textContent = '正在建立SSE连接...';
            outputEl.textContent = '';

            try {
                const response = await fetch(`${API_BASE}/api/chat/stream`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream',
                    },
                    body: JSON.stringify({
                        message: message,
                        thread_id: `test_${Date.now()}`,
                        stream: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                statusEl.className = 'status success';
                statusEl.textContent = 'SSE连接成功，正在接收数据...';

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        statusEl.textContent = 'SSE连接完成';
                        break;
                    }

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || '';

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                outputEl.textContent += `[${new Date().toLocaleTimeString()}] ${JSON.stringify(data, null, 2)}\n`;
                                outputEl.scrollTop = outputEl.scrollHeight;
                            } catch (e) {
                                outputEl.textContent += `[解析错误] ${line}\n`;
                            }
                        }
                    }
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `SSE连接失败: ${error.message}`;
                outputEl.textContent += `错误: ${error.message}\n`;
            }
        }

        function clearSSEOutput() {
            document.getElementById('sseOutput').textContent = '等待测试结果...';
            document.getElementById('sseStatus').className = 'status info';
            document.getElementById('sseStatus').textContent = '准备测试SSE连接';
        }

        // 打字机效果测试
        function testTypewriter() {
            const text = document.getElementById('typewriterText').value;
            const speed = parseInt(document.getElementById('typewriterSpeed').value) || 50;
            const displayEl = document.getElementById('typewriterDisplay');
            const cursorEl = document.getElementById('typewriterCursor');
            
            if (typewriterTimer) {
                clearInterval(typewriterTimer);
            }
            
            displayEl.textContent = '';
            cursorEl.style.display = 'inline';
            
            let index = 0;
            typewriterTimer = setInterval(() => {
                if (index < text.length) {
                    displayEl.textContent += text[index];
                    index++;
                } else {
                    clearInterval(typewriterTimer);
                    cursorEl.style.display = 'none';
                }
            }, speed);
        }

        function stopTypewriter() {
            if (typewriterTimer) {
                clearInterval(typewriterTimer);
                typewriterTimer = null;
            }
            document.getElementById('typewriterCursor').style.display = 'none';
        }

        // API接口测试
        async function testHealthCheck() {
            await testAPI('/health', 'GET', null, 'health check');
        }

        async function testGetTools() {
            await testAPI('/api/tools', 'GET', null, 'get tools');
        }

        async function testRegularChat() {
            await testAPI('/api/chat', 'POST', {
                message: '你好',
                thread_id: `test_${Date.now()}`
            }, 'regular chat');
        }

        async function testAPI(endpoint, method, body, testName) {
            const statusEl = document.getElementById('apiStatus');
            const outputEl = document.getElementById('apiOutput');
            
            statusEl.className = 'status info';
            statusEl.textContent = `正在测试 ${testName}...`;
            
            const startTime = Date.now();
            
            try {
                const options = {
                    method: method,
                    headers: { 'Content-Type': 'application/json' }
                };
                
                if (body) {
                    options.body = JSON.stringify(body);
                }
                
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                const result = await response.json();
                
                statusEl.className = 'status success';
                statusEl.textContent = `${testName} 测试成功 (${duration}ms)`;
                
                outputEl.textContent += `\n[${testName}] ${response.status} ${response.statusText} (${duration}ms)\n`;
                outputEl.textContent += JSON.stringify(result, null, 2) + '\n';
                outputEl.textContent += '---\n';
                outputEl.scrollTop = outputEl.scrollHeight;
                
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `${testName} 测试失败: ${error.message}`;
                outputEl.textContent += `\n[${testName} 错误] ${error.message}\n---\n`;
            }
        }

        function clearApiOutput() {
            document.getElementById('apiOutput').textContent = '等待测试结果...';
            document.getElementById('apiStatus').className = 'status info';
            document.getElementById('apiStatus').textContent = '准备测试API接口';
        }

        // 性能测试
        async function testPerformance() {
            const statusEl = document.getElementById('perfStatus');
            const outputEl = document.getElementById('perfOutput');
            
            statusEl.className = 'status info';
            statusEl.textContent = '正在进行性能测试...';
            outputEl.textContent = '开始性能测试...\n';
            
            const tests = [
                { name: '健康检查', endpoint: '/health', method: 'GET' },
                { name: '获取工具', endpoint: '/api/tools', method: 'GET' },
                { name: '简单聊天', endpoint: '/api/chat', method: 'POST', body: { message: '测试', thread_id: 'perf_test' } }
            ];
            
            for (const test of tests) {
                outputEl.textContent += `\n测试 ${test.name}...\n`;
                
                const times = [];
                for (let i = 0; i < 5; i++) {
                    const startTime = Date.now();
                    try {
                        const options = {
                            method: test.method,
                            headers: { 'Content-Type': 'application/json' }
                        };
                        
                        if (test.body) {
                            options.body = JSON.stringify(test.body);
                        }
                        
                        await fetch(`${API_BASE}${test.endpoint}`, options);
                        const duration = Date.now() - startTime;
                        times.push(duration);
                        outputEl.textContent += `  第${i+1}次: ${duration}ms\n`;
                    } catch (error) {
                        outputEl.textContent += `  第${i+1}次: 失败 (${error.message})\n`;
                    }
                }
                
                if (times.length > 0) {
                    const avg = times.reduce((a, b) => a + b, 0) / times.length;
                    const min = Math.min(...times);
                    const max = Math.max(...times);
                    outputEl.textContent += `  平均: ${avg.toFixed(2)}ms, 最小: ${min}ms, 最大: ${max}ms\n`;
                }
                
                outputEl.scrollTop = outputEl.scrollHeight;
            }
            
            statusEl.className = 'status success';
            statusEl.textContent = '性能测试完成';
            outputEl.textContent += '\n性能测试完成！\n';
        }

        function clearPerfOutput() {
            document.getElementById('perfOutput').textContent = '等待测试结果...';
            document.getElementById('perfStatus').className = 'status info';
            document.getElementById('perfStatus').textContent = '准备进行性能测试';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('流式响应测试页面已加载');
        };
    </script>
</body>
</html>
