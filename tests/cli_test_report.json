{"summary": {"total": 15, "passed": 15, "failed": 0, "pass_rate": 100.0}, "results": [{"test": "持久化配置加载", "success": true, "details": "配置类型: <class 'dict'>", "status": "✅ PASS"}, {"test": "LLM配置加载", "success": true, "details": "LLM类型: ChatOpenAI", "status": "✅ PASS"}, {"test": "MCP工具配置加载", "success": true, "details": "工具数量: 35", "status": "✅ PASS"}, {"test": "检查点存储器创建", "success": true, "details": "存储器类型: AsyncSqliteSaver", "status": "✅ PASS"}, {"test": "LangGraph标准接口验证", "success": true, "details": "包含aget和aput方法", "status": "✅ PASS"}, {"test": "会话创建", "success": true, "details": "会话ID: test_user_7329eb0e", "status": "✅ PASS"}, {"test": "会话配置获取", "success": true, "details": "配置结构: ['configurable']", "status": "✅ PASS"}, {"test": "会话恢复", "success": true, "details": "恢复的会话ID: test_user_7329eb0e", "status": "✅ PASS"}, {"test": "会话清除", "success": true, "details": "新会话ID: test_3eea52fe", "status": "✅ PASS"}, {"test": "Agent应用初始化", "success": true, "details": "应用类型: CompiledStateGraph", "status": "✅ PASS"}, {"test": "工具加载", "success": true, "details": "工具数量: 35", "status": "✅ PASS"}, {"test": "会话管理器初始化", "success": true, "details": "管理器类型: SimpleSessionManager", "status": "✅ PASS"}, {"test": "LangGraph应用标准接口", "success": true, "details": "包含ainvoke和aget_state方法", "status": "✅ PASS"}, {"test": "基本对话功能", "success": true, "details": "对话结果类型: <class 'langgraph.pregel.io.AddableValuesDict'>", "status": "✅ PASS"}, {"test": "状态持久化", "success": true, "details": "消息数量: 2", "status": "✅ PASS"}]}