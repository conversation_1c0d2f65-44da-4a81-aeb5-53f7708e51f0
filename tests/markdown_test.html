<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown渲染效果测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        button:hover {
            background: #f0f0f0;
            border-color: #1890ff;
        }
        .primary-btn {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .primary-btn:hover {
            background: #40a9ff;
        }
        .test-input {
            width: 100%;
            min-height: 200px;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            resize: vertical;
        }
        .test-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            margin-top: 10px;
        }
        .sample-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-bottom: 10px;
        }
        .sample-btn {
            padding: 4px 8px;
            font-size: 12px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
        }
        .sample-btn:hover {
            background: #e0e0e0;
        }
    </style>
</head>
<body>
    <h1>📝 Markdown渲染效果测试</h1>
    
    <!-- Markdown测试器 -->
    <div class="test-container">
        <h2 class="test-title">1. Markdown实时预览测试</h2>
        
        <div class="sample-buttons">
            <button class="sample-btn" onclick="loadSample('basic')">基础语法</button>
            <button class="sample-btn" onclick="loadSample('code')">代码块</button>
            <button class="sample-btn" onclick="loadSample('table')">表格</button>
            <button class="sample-btn" onclick="loadSample('list')">列表</button>
            <button class="sample-btn" onclick="loadSample('quote')">引用</button>
            <button class="sample-btn" onclick="loadSample('complex')">复杂示例</button>
        </div>
        
        <div class="test-controls">
            <button onclick="testMarkdown()" class="primary-btn">发送测试消息</button>
            <button onclick="clearTest()">清空</button>
        </div>
        
        <textarea id="markdownInput" class="test-input" placeholder="在这里输入Markdown内容...">
# 欢迎使用Markdown测试

这是一个**粗体文本**和*斜体文本*的示例。

## 代码示例

```javascript
function hello() {
    console.log("Hello, World!");
}
```

## 列表示例

- 项目1
- 项目2
  - 子项目2.1
  - 子项目2.2

## 表格示例

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |
        </textarea>
        
        <div id="testOutput" class="test-output">
            测试结果将显示在这里...
        </div>
    </div>

    <!-- API测试 -->
    <div class="test-container">
        <h2 class="test-title">2. API集成测试</h2>
        <div class="test-controls">
            <button onclick="testStreamingWithMarkdown()" class="primary-btn">测试流式Markdown响应</button>
            <button onclick="testComplexMarkdown()">测试复杂Markdown</button>
        </div>
        <div id="apiOutput" class="test-output">
            API测试结果将显示在这里...
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // Markdown示例
        const samples = {
            basic: `# 基础Markdown语法测试

## 文本格式
这是**粗体文本**，这是*斜体文本*，这是***粗斜体文本***。

这是~~删除线文本~~，这是\`行内代码\`。

## 链接和图片
[这是一个链接](https://example.com)

## 分隔线
---

> 这是一个引用块
> 可以包含多行内容`,

            code: `# 代码块测试

## JavaScript代码
\`\`\`javascript
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10));
\`\`\`

## Python代码
\`\`\`python
def quick_sort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quick_sort(left) + middle + quick_sort(right)
\`\`\`

## SQL代码
\`\`\`sql
SELECT users.name, COUNT(orders.id) as order_count
FROM users
LEFT JOIN orders ON users.id = orders.user_id
GROUP BY users.id
ORDER BY order_count DESC;
\`\`\``,

            table: `# 表格测试

## 简单表格
| 姓名 | 年龄 | 城市 |
|------|------|------|
| 张三 | 25 | 北京 |
| 李四 | 30 | 上海 |
| 王五 | 28 | 广州 |

## 对齐表格
| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:-------:|-------:|
| 内容1  |  内容2  |  内容3 |
| 较长的内容 | 中等内容 | 短内容 |

## 复杂表格
| 功能 | 状态 | 优先级 | 负责人 | 备注 |
|------|------|--------|--------|------|
| 用户登录 | ✅ 完成 | 高 | 张三 | 已测试 |
| 数据导出 | 🔄 进行中 | 中 | 李四 | 预计下周完成 |
| 报表生成 | ❌ 未开始 | 低 | 王五 | 待需求确认 |`,

            list: `# 列表测试

## 无序列表
- 第一项
- 第二项
  - 子项目2.1
  - 子项目2.2
    - 子子项目2.2.1
    - 子子项目2.2.2
- 第三项

## 有序列表
1. 第一步：准备工作
2. 第二步：执行任务
   1. 子步骤2.1
   2. 子步骤2.2
3. 第三步：验证结果

## 任务列表
- [x] 已完成的任务
- [x] 另一个已完成的任务
- [ ] 待完成的任务
- [ ] 另一个待完成的任务

## 混合列表
1. 有序项目1
   - 无序子项目
   - 另一个无序子项目
2. 有序项目2
   1. 有序子项目
   2. 另一个有序子项目`,

            quote: `# 引用测试

## 简单引用
> 这是一个简单的引用块。

## 多行引用
> 这是一个多行引用的第一行。
> 这是第二行。
> 这是第三行。

## 嵌套引用
> 这是外层引用。
> 
> > 这是嵌套的引用。
> > 可以有多层嵌套。
> 
> 回到外层引用。

## 引用中的其他元素
> ## 引用中的标题
> 
> 引用中可以包含**粗体**和*斜体*文本。
> 
> \`\`\`javascript
> // 引用中的代码块
> console.log("Hello from quote!");
> \`\`\`
> 
> - 引用中的列表项1
> - 引用中的列表项2`,

            complex: `# 复杂Markdown示例

## 项目概述
这是一个**AI智能助手系统**的技术文档，包含了*前端*、*后端*和***数据库***的完整架构。

## 技术栈

### 前端技术
- **框架**: Vue 3 + TypeScript
- **UI库**: MateChat Components
- **状态管理**: Pinia
- **构建工具**: Vite

### 后端技术
- **语言**: Python 3.11+
- **框架**: FastAPI
- **AI框架**: LangGraph
- **工具集成**: MCP (Model Context Protocol)

## 系统架构

\`\`\`mermaid
graph TD
    A[用户界面] --> B[前端应用]
    B --> C[API网关]
    C --> D[智能体核心]
    D --> E[工具集成]
    D --> F[会话管理]
    E --> G[外部服务]
\`\`\`

## API接口文档

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 健康检查 | GET | \`/health\` | 检查服务状态 |
| 聊天接口 | POST | \`/api/chat\` | 发送聊天消息 |
| 流式聊天 | POST | \`/api/chat/stream\` | 流式聊天响应 |
| 文件上传 | POST | \`/api/upload\` | 上传文件 |

## 代码示例

### 前端组件
\`\`\`vue
<template>
  <div class="chat-interface">
    <McMarkdownCard 
      :content="message.content"
      :typing="true"
      theme="light"
    />
  </div>
</template>

<script setup lang="ts">
import { McMarkdownCard } from '@matechat/core';
</script>
\`\`\`

### 后端API
\`\`\`python
@app.post("/api/chat/stream")
async def chat_stream(request: ChatRequest):
    async def generate():
        async for chunk in agent_core.stream_message(
            message=request.message,
            thread_id=request.thread_id
        ):
            yield f"data: {json.dumps(chunk)}\\n\\n"
    
    return StreamingResponse(generate(), media_type="text/event-stream")
\`\`\`

## 注意事项

> ⚠️ **重要提醒**
> 
> 1. 确保所有依赖都已正确安装
> 2. 配置文件中的API密钥需要正确设置
> 3. 数据库连接参数需要根据环境调整

## 待办事项

- [x] 完成基础聊天功能
- [x] 实现流式响应
- [x] 添加文件上传
- [ ] 集成语音识别
- [ ] 添加多语言支持
- [ ] 性能优化

---

*文档最后更新时间: 2024年12月*`
        };

        function loadSample(type) {
            document.getElementById('markdownInput').value = samples[type];
        }

        function testMarkdown() {
            const input = document.getElementById('markdownInput').value;
            const output = document.getElementById('testOutput');
            
            // 这里模拟发送到聊天界面
            output.innerHTML = \`
                <h3>测试消息已发送到聊天界面</h3>
                <p>请在主界面查看Markdown渲染效果。</p>
                <details>
                    <summary>原始Markdown内容</summary>
                    <pre>\${input}</pre>
                </details>
            \`;
            
            // 实际发送到聊天API
            sendTestMessage(input);
        }

        async function sendTestMessage(content) {
            try {
                const response = await fetch(\`\${API_BASE}/api/chat\`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: \`请用Markdown格式回复以下内容：\\n\\n\${content}\`,
                        thread_id: \`markdown_test_\${Date.now()}\`
                    })
                });
                
                if (response.ok) {
                    console.log('测试消息发送成功');
                } else {
                    console.error('发送失败:', response.statusText);
                }
            } catch (error) {
                console.error('发送错误:', error);
            }
        }

        async function testStreamingWithMarkdown() {
            const output = document.getElementById('apiOutput');
            output.innerHTML = '正在测试流式Markdown响应...';
            
            try {
                const response = await fetch(\`\${API_BASE}/api/chat/stream\`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        message: '请用Markdown格式介绍一下你的功能，包括代码示例、表格和列表',
                        thread_id: \`stream_test_\${Date.now()}\`
                    })
                });
                
                if (response.ok) {
                    output.innerHTML = '<h3>✅ 流式响应测试成功</h3><p>请在主界面查看实时渲染效果。</p>';
                } else {
                    output.innerHTML = \`<h3>❌ 测试失败</h3><p>错误: \${response.statusText}</p>\`;
                }
            } catch (error) {
                output.innerHTML = \`<h3>❌ 测试失败</h3><p>错误: \${error.message}</p>\`;
            }
        }

        async function testComplexMarkdown() {
            const complexContent = samples.complex;
            await sendTestMessage(complexContent);
            
            const output = document.getElementById('apiOutput');
            output.innerHTML = '<h3>✅ 复杂Markdown测试已发送</h3><p>请在主界面查看复杂内容的渲染效果。</p>';
        }

        function clearTest() {
            document.getElementById('markdownInput').value = '';
            document.getElementById('testOutput').innerHTML = '测试结果将显示在这里...';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('Markdown测试页面已加载');
            loadSample('basic');
        };
    </script>
</body>
</html>
