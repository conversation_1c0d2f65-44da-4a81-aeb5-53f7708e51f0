<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.2s;
        }
        button:hover {
            background: #f0f0f0;
            border-color: #1890ff;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .primary-btn {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        .primary-btn:hover {
            background: #40a9ff;
        }
        .file-input {
            margin-bottom: 10px;
        }
        .test-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 13px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.warning { background: #fff3cd; color: #856404; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #1890ff;
            transition: width 0.3s ease;
        }
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .file-item {
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 15px;
            background: white;
        }
        .file-name {
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .file-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .file-actions {
            display: flex;
            gap: 5px;
        }
        .file-actions button {
            padding: 4px 8px;
            font-size: 12px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>📁 文件上传功能测试</h1>
    
    <!-- 单文件上传测试 -->
    <div class="test-container">
        <h2 class="test-title">1. 单文件上传测试</h2>
        <div class="test-controls">
            <input type="file" id="singleFile" class="file-input">
            <button onclick="testSingleUpload()" class="primary-btn">上传文件</button>
            <button onclick="clearSingleOutput()">清空输出</button>
        </div>
        <div id="singleStatus" class="status info">准备测试单文件上传</div>
        <div class="progress-bar" id="singleProgress" style="display: none;">
            <div class="progress-fill" id="singleProgressFill" style="width: 0%;"></div>
        </div>
        <div id="singleOutput" class="test-output">等待测试结果...</div>
    </div>

    <!-- 多文件上传测试 -->
    <div class="test-container">
        <h2 class="test-title">2. 多文件上传测试</h2>
        <div class="test-controls">
            <input type="file" id="multipleFiles" class="file-input" multiple>
            <button onclick="testMultipleUpload()" class="primary-btn">批量上传</button>
            <button onclick="clearMultipleOutput()">清空输出</button>
        </div>
        <div id="multipleStatus" class="status info">准备测试多文件上传</div>
        <div class="progress-bar" id="multipleProgress" style="display: none;">
            <div class="progress-fill" id="multipleProgressFill" style="width: 0%;"></div>
        </div>
        <div id="multipleOutput" class="test-output">等待测试结果...</div>
    </div>

    <!-- 文件管理测试 -->
    <div class="test-container">
        <h2 class="test-title">3. 文件管理测试</h2>
        <div class="test-controls">
            <button onclick="listFiles()" class="primary-btn">获取文件列表</button>
            <button onclick="getStorageStats()">存储统计</button>
            <button onclick="clearFileOutput()">清空输出</button>
        </div>
        <div id="fileStatus" class="status info">准备测试文件管理</div>
        <div id="fileOutput" class="test-output">等待测试结果...</div>
        <div id="fileList" class="file-list"></div>
    </div>

    <!-- 存储统计 -->
    <div class="test-container">
        <h2 class="test-title">4. 存储统计信息</h2>
        <div class="test-controls">
            <button onclick="refreshStats()" class="primary-btn">刷新统计</button>
        </div>
        <div id="statsContainer" class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalFiles">-</div>
                <div class="stat-label">总文件数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalSize">-</div>
                <div class="stat-label">总大小 (MB)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="imageCount">-</div>
                <div class="stat-label">图片文件</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="documentCount">-</div>
                <div class="stat-label">文档文件</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        // 单文件上传测试
        async function testSingleUpload() {
            const fileInput = document.getElementById('singleFile');
            const statusEl = document.getElementById('singleStatus');
            const outputEl = document.getElementById('singleOutput');
            const progressEl = document.getElementById('singleProgress');
            const progressFill = document.getElementById('singleProgressFill');
            
            if (!fileInput.files.length) {
                statusEl.className = 'status error';
                statusEl.textContent = '请选择文件';
                return;
            }

            const file = fileInput.files[0];
            statusEl.className = 'status info';
            statusEl.textContent = `正在上传: ${file.name}`;
            outputEl.textContent = '';
            progressEl.style.display = 'block';
            progressFill.style.width = '0%';

            try {
                const formData = new FormData();
                formData.append('file', file);

                const xhr = new XMLHttpRequest();
                
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressFill.style.width = percentComplete + '%';
                    }
                });

                xhr.onload = function() {
                    progressEl.style.display = 'none';
                    if (xhr.status === 200) {
                        const result = JSON.parse(xhr.responseText);
                        statusEl.className = 'status success';
                        statusEl.textContent = '上传成功';
                        outputEl.textContent = JSON.stringify(result, null, 2);
                    } else {
                        statusEl.className = 'status error';
                        statusEl.textContent = '上传失败';
                        outputEl.textContent = `错误: ${xhr.status} ${xhr.statusText}`;
                    }
                };

                xhr.onerror = function() {
                    progressEl.style.display = 'none';
                    statusEl.className = 'status error';
                    statusEl.textContent = '上传失败';
                    outputEl.textContent = '网络错误';
                };

                xhr.open('POST', `${API_BASE}/api/upload`);
                xhr.send(formData);

            } catch (error) {
                progressEl.style.display = 'none';
                statusEl.className = 'status error';
                statusEl.textContent = '上传失败';
                outputEl.textContent = `错误: ${error.message}`;
            }
        }

        // 多文件上传测试
        async function testMultipleUpload() {
            const fileInput = document.getElementById('multipleFiles');
            const statusEl = document.getElementById('multipleStatus');
            const outputEl = document.getElementById('multipleOutput');
            const progressEl = document.getElementById('multipleProgress');
            const progressFill = document.getElementById('multipleProgressFill');
            
            if (!fileInput.files.length) {
                statusEl.className = 'status error';
                statusEl.textContent = '请选择文件';
                return;
            }

            const files = Array.from(fileInput.files);
            statusEl.className = 'status info';
            statusEl.textContent = `正在上传 ${files.length} 个文件`;
            outputEl.textContent = '';
            progressEl.style.display = 'block';
            progressFill.style.width = '0%';

            try {
                const formData = new FormData();
                files.forEach(file => {
                    formData.append('files', file);
                });

                const response = await fetch(`${API_BASE}/api/upload/multiple`, {
                    method: 'POST',
                    body: formData
                });

                progressEl.style.display = 'none';

                if (response.ok) {
                    const result = await response.json();
                    statusEl.className = 'status success';
                    statusEl.textContent = '批量上传完成';
                    outputEl.textContent = JSON.stringify(result, null, 2);
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = '批量上传失败';
                    outputEl.textContent = `错误: ${response.status} ${response.statusText}`;
                }

            } catch (error) {
                progressEl.style.display = 'none';
                statusEl.className = 'status error';
                statusEl.textContent = '批量上传失败';
                outputEl.textContent = `错误: ${error.message}`;
            }
        }

        // 获取文件列表
        async function listFiles() {
            const statusEl = document.getElementById('fileStatus');
            const outputEl = document.getElementById('fileOutput');
            const fileListEl = document.getElementById('fileList');
            
            statusEl.className = 'status info';
            statusEl.textContent = '正在获取文件列表...';
            
            try {
                const response = await fetch(`${API_BASE}/api/files`);
                
                if (response.ok) {
                    const result = await response.json();
                    statusEl.className = 'status success';
                    statusEl.textContent = `获取到 ${result.files.length} 个文件`;
                    outputEl.textContent = JSON.stringify(result, null, 2);
                    
                    // 显示文件列表
                    fileListEl.innerHTML = '';
                    result.files.forEach(file => {
                        const fileItem = document.createElement('div');
                        fileItem.className = 'file-item';
                        fileItem.innerHTML = `
                            <div class="file-name">${file.filename}</div>
                            <div class="file-meta">
                                ${formatFileSize(file.file_size)} • ${file.file_type} • ${new Date(file.upload_time).toLocaleString()}
                            </div>
                            <div class="file-actions">
                                <button onclick="previewFile('${file.file_id}')">预览</button>
                                <button onclick="downloadFile('${file.download_url}', '${file.filename}')">下载</button>
                                <button onclick="deleteFile('${file.file_id}')">删除</button>
                            </div>
                        `;
                        fileListEl.appendChild(fileItem);
                    });
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = '获取文件列表失败';
                    outputEl.textContent = `错误: ${response.status} ${response.statusText}`;
                }
                
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '获取文件列表失败';
                outputEl.textContent = `错误: ${error.message}`;
            }
        }

        // 获取存储统计
        async function getStorageStats() {
            try {
                const response = await fetch(`${API_BASE}/api/storage/stats`);
                
                if (response.ok) {
                    const stats = await response.json();
                    updateStatsDisplay(stats);
                }
            } catch (error) {
                console.error('获取存储统计失败:', error);
            }
        }

        // 更新统计显示
        function updateStatsDisplay(stats) {
            document.getElementById('totalFiles').textContent = stats.total_files;
            document.getElementById('totalSize').textContent = stats.total_size_mb;
            
            const imageCount = stats.type_stats.image ? stats.type_stats.image.count : 0;
            const documentCount = stats.type_stats.document ? stats.type_stats.document.count : 0;
            
            document.getElementById('imageCount').textContent = imageCount;
            document.getElementById('documentCount').textContent = documentCount;
        }

        // 工具函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function previewFile(fileId) {
            window.open(`${API_BASE}/api/files/${fileId}`, '_blank');
        }

        function downloadFile(url, filename) {
            const link = document.createElement('a');
            link.href = `${API_BASE}${url}`;
            link.download = filename;
            link.click();
        }

        async function deleteFile(fileId) {
            if (confirm('确定要删除这个文件吗？')) {
                try {
                    const response = await fetch(`${API_BASE}/api/files/${fileId}`, {
                        method: 'DELETE'
                    });
                    
                    if (response.ok) {
                        alert('文件删除成功');
                        listFiles(); // 刷新文件列表
                        refreshStats(); // 刷新统计
                    } else {
                        alert('文件删除失败');
                    }
                } catch (error) {
                    alert('删除文件时出错: ' + error.message);
                }
            }
        }

        function refreshStats() {
            getStorageStats();
        }

        // 清空输出函数
        function clearSingleOutput() {
            document.getElementById('singleOutput').textContent = '等待测试结果...';
            document.getElementById('singleStatus').className = 'status info';
            document.getElementById('singleStatus').textContent = '准备测试单文件上传';
            document.getElementById('singleProgress').style.display = 'none';
        }

        function clearMultipleOutput() {
            document.getElementById('multipleOutput').textContent = '等待测试结果...';
            document.getElementById('multipleStatus').className = 'status info';
            document.getElementById('multipleStatus').textContent = '准备测试多文件上传';
            document.getElementById('multipleProgress').style.display = 'none';
        }

        function clearFileOutput() {
            document.getElementById('fileOutput').textContent = '等待测试结果...';
            document.getElementById('fileStatus').className = 'status info';
            document.getElementById('fileStatus').textContent = '准备测试文件管理';
            document.getElementById('fileList').innerHTML = '';
        }

        // 页面加载完成后初始化
        window.onload = function() {
            console.log('文件上传测试页面已加载');
            refreshStats();
        };
    </script>
</body>
</html>
