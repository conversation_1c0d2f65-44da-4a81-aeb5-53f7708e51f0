"""
简化的核心组件单元测试
测试实际存在的核心组件功能
"""

import pytest
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

@pytest.mark.unit
class TestConfigManager:
    """配置管理器测试"""
    
    def test_config_manager_initialization(self):
        """测试配置管理器初始化"""
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        assert config_manager is not None
        assert hasattr(config_manager, '_configs')
    
    def test_get_llm_config(self):
        """测试LLM配置获取"""
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        llm_config = config_manager.get_llm_config()
        
        # 即使没有配置文件，也应该返回字典
        assert isinstance(llm_config, dict)
    
    def test_get_mcp_config(self):
        """测试MCP配置获取"""
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        mcp_config = config_manager.get_mcp_config()
        
        assert isinstance(mcp_config, dict)
    
    def test_get_persistence_config(self):
        """测试持久化配置获取"""
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        persistence_config = config_manager.get_persistence_config()
        
        assert isinstance(persistence_config, dict)
    
    def test_config_validation(self):
        """测试配置验证"""
        from core.config_manager import ConfigManager

        config_manager = ConfigManager()

        # 测试配置验证方法
        result = config_manager.validate_config()
        # validate_config 返回字典，不是布尔值
        assert isinstance(result, dict)

@pytest.mark.unit
class TestAgentCore:
    """Agent核心测试"""
    
    def test_agent_core_initialization(self):
        """测试Agent核心初始化"""
        from core.agent_core import AgentCore

        agent_core = AgentCore()
        assert agent_core is not None
        assert hasattr(agent_core, 'llm')
        assert hasattr(agent_core, 'tools')
        # workflow 在初始化后才创建，所以这里不检查

@pytest.mark.unit 
class TestErrorHandler:
    """错误处理器测试"""
    
    def test_error_handler_import(self):
        """测试错误处理器导入"""
        from core.error_handler import ErrorHandler
        
        error_handler = ErrorHandler()
        assert error_handler is not None

@pytest.mark.unit
class TestMainModule:
    """主模块测试"""
    
    def test_main_module_import(self):
        """测试主模块导入"""
        import main
        
        assert hasattr(main, 'initialize_agent')
        assert hasattr(main, 'load_persistence_config')
        assert hasattr(main, 'create_checkpointer')
        assert hasattr(main, 'SimpleSessionManager')
    
    def test_persistence_config_loading(self):
        """测试持久化配置加载"""
        import main
        
        # 测试配置加载函数
        config = main.load_persistence_config()
        assert isinstance(config, dict)
    
    def test_session_manager_class(self):
        """测试会话管理器类"""
        import main
        
        # 测试会话管理器类存在
        assert hasattr(main, 'SimpleSessionManager')
        session_manager = main.SimpleSessionManager()
        assert session_manager is not None

@pytest.mark.unit
class TestUtilities:
    """工具函数测试"""
    
    def test_uuid_generation(self):
        """测试UUID生成"""
        import uuid
        
        # 测试UUID生成
        test_id = str(uuid.uuid4())
        assert isinstance(test_id, str)
        assert len(test_id) > 0
    
    def test_path_operations(self):
        """测试路径操作"""
        from pathlib import Path
        
        # 测试路径操作
        test_path = Path("test")
        assert isinstance(test_path, Path)
        assert str(test_path) == "test"

@pytest.mark.unit
class TestImports:
    """导入测试"""
    
    def test_langgraph_imports(self):
        """测试LangGraph导入"""
        try:
            from langgraph.graph import StateGraph
            from langgraph.prebuilt import ToolNode

            assert StateGraph is not None
            assert ToolNode is not None

            # 尝试导入MessagesState，可能在不同位置
            try:
                from langchain_core.messages import MessagesState
                assert MessagesState is not None
            except ImportError:
                try:
                    from langgraph.graph import MessagesState
                    assert MessagesState is not None
                except ImportError:
                    # 如果都找不到，跳过这个检查
                    pass

            # 尝试导入AsyncSqliteSaver，如果失败则跳过
            try:
                from langgraph_checkpoint_sqlite import AsyncSqliteSaver
                assert AsyncSqliteSaver is not None
            except ImportError:
                # 如果没有安装sqlite包，跳过这个检查
                pass
        except ImportError as e:
            pytest.fail(f"LangGraph导入失败: {e}")
    
    def test_langchain_imports(self):
        """测试LangChain导入"""
        try:
            from langchain_core.messages import HumanMessage, AIMessage
            from langchain_core.tools import tool
            
            assert HumanMessage is not None
            assert AIMessage is not None
            assert tool is not None
        except ImportError as e:
            pytest.fail(f"LangChain导入失败: {e}")

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
