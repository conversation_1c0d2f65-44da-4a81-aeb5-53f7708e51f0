"""
核心组件单元测试
测试核心业务逻辑组件
"""
import pytest
import asyncio
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

@pytest.mark.unit
@pytest.mark.cli
class TestConfigManager:
    """配置管理器测试"""
    
    def test_load_llm_config(self, test_config_dir):
        """测试LLM配置加载"""
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager(str(test_config_dir))
        llm_config = config_manager.load_llm_config()
        
        assert llm_config is not None
        assert llm_config['provider'] == 'zhipu'
        assert llm_config['model_name'] == 'GLM-4-Flash'
    
    def test_load_mcp_config(self, test_config_dir):
        """测试MCP配置加载"""
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager(str(test_config_dir))
        mcp_config = config_manager.load_mcp_config()
        
        assert mcp_config is not None
        assert 'servers' in mcp_config
    
    def test_load_persistence_config(self, test_config_dir):
        """测试持久化配置加载"""
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager(str(test_config_dir))
        persistence_config = config_manager.load_persistence_config()
        
        assert persistence_config is not None
        assert persistence_config['type'] == 'sqlite'
    
    def test_invalid_config_path(self):
        """测试无效配置路径"""
        from core.config_manager import ConfigManager
        
        with pytest.raises(FileNotFoundError):
            config_manager = ConfigManager("/invalid/path")
            config_manager.load_llm_config()

@pytest.mark.unit
@pytest.mark.cli
class TestSessionManager:
    """会话管理器测试"""
    
    def test_create_session(self):
        """测试创建会话"""
        from core.session_manager import EnhancedSessionManager
        
        session_manager = EnhancedSessionManager()
        session_id = session_manager.create_session("test_user")
        
        assert session_id is not None
        assert len(session_id) > 0
        assert session_id in session_manager.sessions
    
    def test_get_session_config(self):
        """测试获取会话配置"""
        from core.session_manager import EnhancedSessionManager
        
        session_manager = EnhancedSessionManager()
        session_id = session_manager.create_session("test_user")
        config = session_manager.get_session_config(session_id)
        
        assert config is not None
        assert 'configurable' in config
        assert config['configurable']['thread_id'] == session_id
    
    def test_list_sessions(self):
        """测试列出会话"""
        from core.session_manager import EnhancedSessionManager
        
        session_manager = EnhancedSessionManager()
        session_id1 = session_manager.create_session("user1")
        session_id2 = session_manager.create_session("user2")
        
        sessions = session_manager.list_sessions()
        
        assert len(sessions) >= 2
        assert session_id1 in [s['session_id'] for s in sessions]
        assert session_id2 in [s['session_id'] for s in sessions]
    
    def test_session_cleanup(self):
        """测试会话清理"""
        from core.session_manager import EnhancedSessionManager
        
        session_manager = EnhancedSessionManager()
        session_id = session_manager.create_session("test_user")
        
        # 验证会话存在
        assert session_id in session_manager.sessions
        
        # 清理会话
        session_manager.cleanup_session(session_id)
        
        # 验证会话已清理
        assert session_id not in session_manager.sessions

@pytest.mark.unit
@pytest.mark.langgraph
class TestAgentCore:
    """智能体核心测试"""
    
    @pytest.mark.asyncio
    async def test_agent_initialization(self, test_config_dir):
        """测试智能体初始化"""
        # 临时修改工作目录
        original_cwd = os.getcwd()
        os.chdir(test_config_dir.parent)
        
        try:
            from core.agent_core import AgentCore
            
            agent_core = AgentCore()
            await agent_core.initialize()
            
            assert agent_core.llm is not None
            assert agent_core.tools is not None
            assert len(agent_core.tools) > 0
            assert agent_core.checkpointer is not None
        finally:
            os.chdir(original_cwd)
    
    @pytest.mark.asyncio
    async def test_create_workflow(self, test_config_dir):
        """测试创建工作流"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir.parent)
        
        try:
            from core.agent_core import AgentCore
            
            agent_core = AgentCore()
            await agent_core.initialize()
            workflow = agent_core.create_workflow()
            
            assert workflow is not None
            assert hasattr(workflow, 'ainvoke')
            assert hasattr(workflow, 'astream')
        finally:
            os.chdir(original_cwd)
    
    @pytest.mark.asyncio
    async def test_process_message(self, test_config_dir):
        """测试消息处理"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir.parent)
        
        try:
            from core.agent_core import AgentCore
            from langchain_core.messages import HumanMessage
            
            agent_core = AgentCore()
            await agent_core.initialize()
            workflow = agent_core.create_workflow()
            
            # 创建测试配置
            config = {
                "configurable": {
                    "thread_id": "test_thread",
                    "user_id": "test_user"
                }
            }
            
            # 模拟消息处理（不实际调用LLM）
            test_message = HumanMessage(content="测试消息")
            
            # 验证工作流可以接收消息
            assert workflow is not None
            
        finally:
            os.chdir(original_cwd)

@pytest.mark.unit
class TestUtilities:
    """工具函数测试"""
    
    def test_generate_session_id(self):
        """测试会话ID生成"""
        from core.session_manager import EnhancedSessionManager
        
        session_manager = EnhancedSessionManager()
        session_id1 = session_manager._generate_session_id()
        session_id2 = session_manager._generate_session_id()
        
        assert session_id1 != session_id2
        assert len(session_id1) > 0
        assert len(session_id2) > 0
    
    def test_validate_config(self):
        """测试配置验证"""
        from core.config_manager import ConfigManager
        
        # 测试有效配置
        valid_config = {
            'provider': 'zhipu',
            'model_name': 'GLM-4-Flash'
        }
        
        assert ConfigManager._validate_llm_config(valid_config) is True
        
        # 测试无效配置
        invalid_config = {
            'provider': 'zhipu'
            # 缺少 model_name
        }
        
        assert ConfigManager._validate_llm_config(invalid_config) is False

@pytest.mark.unit
@pytest.mark.performance
class TestPerformance:
    """性能相关测试"""
    
    def test_config_loading_performance(self, test_config_dir, performance_monitor):
        """测试配置加载性能"""
        from core.config_manager import ConfigManager
        
        def load_configs():
            config_manager = ConfigManager(str(test_config_dir))
            config_manager.load_llm_config()
            config_manager.load_mcp_config()
            config_manager.load_persistence_config()
        
        metrics = performance_monitor(load_configs)
        
        # 配置加载应该在1秒内完成
        assert metrics['duration'] < 1.0
        assert metrics['success'] is True
    
    def test_session_creation_performance(self, performance_monitor):
        """测试会话创建性能"""
        from core.session_manager import EnhancedSessionManager
        
        def create_multiple_sessions():
            session_manager = EnhancedSessionManager()
            for i in range(100):
                session_manager.create_session(f"user_{i}")
        
        metrics = performance_monitor(create_multiple_sessions)
        
        # 创建100个会话应该在2秒内完成
        assert metrics['duration'] < 2.0
        assert metrics['success'] is True

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
