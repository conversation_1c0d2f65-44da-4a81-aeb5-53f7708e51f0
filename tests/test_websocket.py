#!/usr/bin/env python3
"""
WebSocket测试脚本
测试简化Web API的WebSocket功能
"""

import asyncio
import json
import websockets
import uuid


async def test_websocket():
    """测试WebSocket连接和消息传递"""
    
    thread_id = f"test_{uuid.uuid4().hex[:8]}"
    uri = f"ws://localhost:8000/ws/{thread_id}"
    
    print(f"🔗 连接到WebSocket: {uri}")
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 等待连接确认消息
            response = await websocket.recv()
            connection_data = json.loads(response)
            print(f"📨 连接确认: {connection_data}")
            
            # 发送测试消息
            test_messages = [
                "你好，这是WebSocket测试",
                "请告诉我当前时间",
                "测试流式响应功能",
                "你有什么功能？"
            ]
            
            for i, message in enumerate(test_messages, 1):
                print(f"\n🚀 发送消息 {i}: {message}")
                
                # 发送聊天消息
                await websocket.send(json.dumps({
                    "type": "chat",
                    "message": message
                }))
                
                # 接收响应
                ai_response = ""
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                        data = json.loads(response)
                        
                        if data.get("type") == "user_message":
                            print(f"📤 用户消息确认: {data.get('message')}")
                        
                        elif data.get("type") == "ai_chunk":
                            chunk = data.get("chunk", "")
                            ai_response += chunk
                            print(chunk, end="", flush=True)
                        
                        elif data.get("type") == "ai_complete":
                            print(f"\n✅ AI回复完成")
                            break
                        
                        elif data.get("type") == "error":
                            print(f"❌ 错误: {data.get('error')}")
                            break
                    
                    except asyncio.TimeoutError:
                        print("\n⏰ 响应超时")
                        break
                
                print(f"🤖 完整AI回复: {ai_response}")
                
                # 等待一下再发送下一条消息
                await asyncio.sleep(1)
            
            print("\n🎉 WebSocket测试完成")
    
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")


async def test_multiple_connections():
    """测试多个WebSocket连接"""
    
    print("\n🔗 测试多个WebSocket连接...")
    
    async def single_connection_test(connection_id):
        thread_id = f"multi_test_{connection_id}"
        uri = f"ws://localhost:8000/ws/{thread_id}"
        
        try:
            async with websockets.connect(uri) as websocket:
                # 等待连接确认
                await websocket.recv()
                
                # 发送测试消息
                await websocket.send(json.dumps({
                    "type": "chat",
                    "message": f"这是连接 {connection_id} 的测试消息"
                }))
                
                # 接收响应
                response_count = 0
                while response_count < 3:  # 期望收到用户消息确认、AI块、完成信号
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        data = json.loads(response)
                        response_count += 1
                        
                        if data.get("type") == "ai_complete":
                            break
                    
                    except asyncio.TimeoutError:
                        break
                
                print(f"✅ 连接 {connection_id} 测试完成")
        
        except Exception as e:
            print(f"❌ 连接 {connection_id} 测试失败: {e}")
    
    # 并发测试3个连接
    tasks = [single_connection_test(i) for i in range(1, 4)]
    await asyncio.gather(*tasks)
    
    print("🎉 多连接测试完成")


async def main():
    """主测试函数"""
    print("🧪 开始WebSocket功能测试")
    print("=" * 50)
    
    # 基本WebSocket测试
    await test_websocket()
    
    # 多连接测试
    await test_multiple_connections()
    
    print("\n" + "=" * 50)
    print("🏁 所有WebSocket测试完成")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
