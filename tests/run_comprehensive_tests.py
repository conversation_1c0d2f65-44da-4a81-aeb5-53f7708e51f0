#!/usr/bin/env python3
"""
综合测试运行器
执行完整的测试套件并生成报告
"""
import os
import sys
import time
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Any
import argparse

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

class ComprehensiveTestRunner:
    """综合测试运行器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.test_results: Dict[str, Any] = {}
        self.start_time = time.time()
        
    def run_test_suite(self, test_type: str, test_path: str, markers: List[str] = None) -> Dict[str, Any]:
        """运行测试套件"""
        print(f"\n🚀 开始运行 {test_type} 测试...")
        print("=" * 60)
        
        # 构建pytest命令
        cmd = [
            sys.executable, "-m", "pytest",
            test_path,
            "-v",
            "--tb=short",
            "--durations=10",
            f"--junitxml={self.project_root}/tests/reports/{test_type}_results.xml"
        ]
        
        # 添加标记过滤
        if markers:
            for marker in markers:
                cmd.extend(["-m", marker])
        
        # 运行测试
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            success = result.returncode == 0
            duration = time.time() - start_time
            
            test_result = {
                'success': success,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'return_code': result.returncode
            }
            
            if success:
                print(f"✅ {test_type} 测试通过 (耗时: {duration:.2f}秒)")
            else:
                print(f"❌ {test_type} 测试失败 (耗时: {duration:.2f}秒)")
                print(f"错误输出: {result.stderr}")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_type} 测试超时")
            return {
                'success': False,
                'duration': 300,
                'error': 'Test timeout',
                'return_code': -1
            }
        except Exception as e:
            print(f"💥 {test_type} 测试执行异常: {e}")
            return {
                'success': False,
                'duration': 0,
                'error': str(e),
                'return_code': -1
            }
    
    def run_unit_tests(self) -> Dict[str, Any]:
        """运行单元测试"""
        return self.run_test_suite(
            "单元测试",
            "tests/unit/",
            ["unit"]
        )
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """运行集成测试"""
        return self.run_test_suite(
            "集成测试",
            "tests/integration/",
            ["integration"]
        )
    
    def run_e2e_tests(self) -> Dict[str, Any]:
        """运行端到端测试"""
        return self.run_test_suite(
            "端到端测试",
            "tests/e2e/",
            ["e2e"]
        )
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """运行性能测试"""
        return self.run_test_suite(
            "性能测试",
            "tests/",
            ["performance"]
        )
    
    def run_langgraph_compliance_tests(self) -> Dict[str, Any]:
        """运行LangGraph合规性测试"""
        print(f"\n🔍 开始运行 LangGraph合规性 测试...")
        print("=" * 60)
        
        start_time = time.time()
        try:
            result = subprocess.run(
                [sys.executable, "tests/test_langgraph_compliance.py"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            success = result.returncode == 0
            duration = time.time() - start_time
            
            if success:
                print(f"✅ LangGraph合规性测试通过 (耗时: {duration:.2f}秒)")
            else:
                print(f"❌ LangGraph合规性测试失败 (耗时: {duration:.2f}秒)")
                print(f"错误输出: {result.stderr}")
            
            return {
                'success': success,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'return_code': result.returncode
            }
            
        except Exception as e:
            print(f"💥 LangGraph合规性测试执行异常: {e}")
            return {
                'success': False,
                'duration': 0,
                'error': str(e),
                'return_code': -1
            }
    
    def run_all_tests(self, include_slow: bool = False) -> Dict[str, Any]:
        """运行所有测试"""
        print("🎯 开始综合测试套件")
        print("=" * 60)
        
        # 创建报告目录
        reports_dir = self.project_root / "tests" / "reports"
        reports_dir.mkdir(exist_ok=True)
        
        # 运行各类测试
        results = {}
        
        # 1. LangGraph合规性测试（最重要）
        results['langgraph_compliance'] = self.run_langgraph_compliance_tests()
        
        # 2. 单元测试
        results['unit'] = self.run_unit_tests()
        
        # 3. 集成测试
        results['integration'] = self.run_integration_tests()
        
        # 4. 端到端测试
        if include_slow:
            results['e2e'] = self.run_e2e_tests()
            results['performance'] = self.run_performance_tests()
        
        # 生成综合报告
        self.test_results = results
        self.generate_comprehensive_report()
        
        return results
    
    def generate_comprehensive_report(self):
        """生成综合测试报告"""
        total_duration = time.time() - self.start_time
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        failed_tests = total_tests - passed_tests
        
        # 生成报告
        report = {
            'summary': {
                'total_test_suites': total_tests,
                'passed_suites': passed_tests,
                'failed_suites': failed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                'total_duration': total_duration,
                'timestamp': time.time()
            },
            'detailed_results': self.test_results,
            'recommendations': self._generate_recommendations()
        }
        
        # 保存报告
        report_path = self.project_root / "tests" / "reports" / "comprehensive_test_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # 打印摘要
        print("\n" + "=" * 60)
        print("📊 综合测试报告")
        print("=" * 60)
        print(f"总测试套件数: {total_tests}")
        print(f"通过套件数: {passed_tests}")
        print(f"失败套件数: {failed_tests}")
        print(f"成功率: {report['summary']['success_rate']:.1f}%")
        print(f"总耗时: {total_duration:.2f}秒")
        
        # 详细结果
        for test_type, result in self.test_results.items():
            status = "✅ 通过" if result.get('success', False) else "❌ 失败"
            duration = result.get('duration', 0)
            print(f"  {test_type}: {status} ({duration:.2f}秒)")
        
        print(f"\n📄 详细报告已保存: {report_path}")
        
        # 返回总体成功状态
        return passed_tests == total_tests
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        for test_type, result in self.test_results.items():
            if not result.get('success', False):
                recommendations.append(f"修复 {test_type} 测试失败问题")
            
            duration = result.get('duration', 0)
            if duration > 60:  # 超过1分钟
                recommendations.append(f"优化 {test_type} 测试性能，当前耗时 {duration:.1f}秒")
        
        if not recommendations:
            recommendations.append("所有测试通过，系统状态良好")
        
        return recommendations

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="综合测试运行器")
    parser.add_argument("--type", choices=["unit", "integration", "e2e", "performance", "compliance", "all"], 
                       default="all", help="测试类型")
    parser.add_argument("--include-slow", action="store_true", help="包含慢速测试")
    parser.add_argument("--project-root", type=str, help="项目根目录")
    
    args = parser.parse_args()
    
    # 确定项目根目录
    if args.project_root:
        project_root = Path(args.project_root)
    else:
        project_root = Path(__file__).parent.parent
    
    # 创建测试运行器
    runner = ComprehensiveTestRunner(project_root)
    
    # 运行指定测试
    if args.type == "unit":
        result = runner.run_unit_tests()
    elif args.type == "integration":
        result = runner.run_integration_tests()
    elif args.type == "e2e":
        result = runner.run_e2e_tests()
    elif args.type == "performance":
        result = runner.run_performance_tests()
    elif args.type == "compliance":
        result = runner.run_langgraph_compliance_tests()
    else:  # all
        result = runner.run_all_tests(args.include_slow)
    
    # 根据结果设置退出码
    if isinstance(result, dict):
        success = result.get('success', False)
    else:
        success = runner.generate_comprehensive_report()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
