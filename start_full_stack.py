#!/usr/bin/env python3
"""
全栈启动脚本
同时启动后端API服务和前端开发服务器
"""

import asyncio
import subprocess
import sys
import os
import signal
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在关闭所有服务...")
    sys.exit(0)

def check_node_installed():
    """检查Node.js是否安装"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js未安装")
        return False

def check_npm_installed():
    """检查npm是否安装"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ npm版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ npm未安装")
            return False
    except FileNotFoundError:
        print("❌ npm未安装")
        return False

def install_frontend_deps():
    """安装前端依赖"""
    frontend_dir = project_root / "frontend"
    if not (frontend_dir / "node_modules").exists():
        print("📦 正在安装前端依赖...")
        try:
            result = subprocess.run(
                ['npm', 'install'], 
                cwd=frontend_dir, 
                capture_output=True, 
                text=True
            )
            if result.returncode == 0:
                print("✅ 前端依赖安装成功")
                return True
            else:
                print(f"❌ 前端依赖安装失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 前端依赖安装失败: {e}")
            return False
    else:
        print("✅ 前端依赖已存在")
        return True

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端API服务...")
    try:
        # 使用uv启动后端
        process = subprocess.Popen(
            ['uv', 'run', 'start_web.py'],
            cwd=project_root,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        return process
    except Exception as e:
        print(f"❌ 后端启动失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    print("🎨 启动前端开发服务器...")
    frontend_dir = project_root / "frontend"
    try:
        process = subprocess.Popen(
            ['npm', 'run', 'dev'],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        return process
    except Exception as e:
        print(f"❌ 前端启动失败: {e}")
        return None

def show_startup_info():
    """显示启动信息"""
    print("\n" + "="*80)
    print("🚀 my_project 全栈AI智能助手系统")
    print("="*80)
    print("📋 服务信息:")
    print("  🎨 前端界面: http://localhost:3000")
    print("  🔧 后端API: http://localhost:8000")
    print("  📚 API文档: http://localhost:8000/docs")
    print("  ❤️ 健康检查: http://localhost:8000/health")
    print("")
    print("🎯 功能特性:")
    print("  • 现代化AI聊天界面 (MateChat)")
    print("  • 实时WebSocket通信")
    print("  • 智谱GLM-4-Flash模型")
    print("  • 会话历史持久化")
    print("  • 响应式设计")
    print("")
    print("⚡ 快速体验:")
    print("  1. 浏览器访问: http://localhost:3000")
    print("  2. 开始与AI助手对话")
    print("")
    print("🔥 正在启动服务...")
    print("="*80)

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 显示启动信息
    show_startup_info()
    
    # 检查环境
    if not check_node_installed() or not check_npm_installed():
        print("\n❌ 请先安装Node.js和npm")
        print("   下载地址: https://nodejs.org/")
        sys.exit(1)
    
    # 安装前端依赖
    if not install_frontend_deps():
        sys.exit(1)
    
    # 启动后端服务
    backend_process = start_backend()
    if not backend_process:
        sys.exit(1)
    
    # 等待后端启动
    print("⏳ 等待后端服务启动...")
    time.sleep(3)
    
    # 启动前端服务
    frontend_process = start_frontend()
    if not frontend_process:
        backend_process.terminate()
        sys.exit(1)
    
    print("\n✅ 全栈服务启动成功!")
    print("🌐 前端界面: http://localhost:3000")
    print("🔧 后端API: http://localhost:8000")
    print("\n按 Ctrl+C 停止所有服务")
    
    try:
        # 等待进程结束
        while True:
            # 检查进程状态
            if backend_process.poll() is not None:
                print("❌ 后端服务意外停止")
                break
            if frontend_process.poll() is not None:
                print("❌ 前端服务意外停止")
                break
            time.sleep(1)
    
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
    
    finally:
        # 清理进程
        if backend_process and backend_process.poll() is None:
            backend_process.terminate()
            backend_process.wait()
        
        if frontend_process and frontend_process.poll() is None:
            frontend_process.terminate()
            frontend_process.wait()
        
        print("👋 所有服务已停止")

if __name__ == "__main__":
    main()
