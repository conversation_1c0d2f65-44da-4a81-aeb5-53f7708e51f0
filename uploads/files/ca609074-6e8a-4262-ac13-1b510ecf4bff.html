<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=5,IE=9" ><![endif]-->
<!DOCTYPE html>
<html>
<head>
<title>woodenfish 主流程详细架构.html</title>
<meta charset="utf-8"/>
</head>
<body>
<div class="mxgraph" style="max-width:100%;border:1px solid transparent;" data-mxgraph="{&quot;highlight&quot;:&quot;#0000ff&quot;,&quot;nav&quot;:true,&quot;resize&quot;:true,&quot;xml&quot;:&quot;&lt;mxfile host=\&quot;Electron\&quot; agent=\&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36\&quot; version=\&quot;26.2.15\&quot;&gt;&lt;diagram name=\&quot;第 1 页\&quot; id=\&quot;G5Rb8x5kOr6_LjMMFZSz\&quot;&gt;&lt;mxGraphModel dx=\&quot;1363\&quot; dy=\&quot;2459\&quot; grid=\&quot;1\&quot; gridSize=\&quot;10\&quot; guides=\&quot;1\&quot; tooltips=\&quot;1\&quot; connect=\&quot;1\&quot; arrows=\&quot;1\&quot; fold=\&quot;1\&quot; page=\&quot;1\&quot; pageScale=\&quot;1\&quot; pageWidth=\&quot;900\&quot; pageHeight=\&quot;1600\&quot; math=\&quot;0\&quot; shadow=\&quot;0\&quot;&gt;&lt;root&gt;&lt;mxCell id=\&quot;0\&quot;/&gt;&lt;mxCell id=\&quot;1\&quot; parent=\&quot;0\&quot;/&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--1\&quot; value=\&quot;MCP工具进程\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;908\&quot; y=\&quot;1398\&quot; width=\&quot;330\&quot; height=\&quot;152\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--2\&quot; value=\&quot;智能体/工具链核心\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;fontSize=22;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;20\&quot; y=\&quot;547\&quot; width=\&quot;2562\&quot; height=\&quot;779\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--3\&quot; value=\&quot;FastAPI后端\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;670\&quot; y=\&quot;-39\&quot; width=\&quot;847\&quot; height=\&quot;454\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--4\&quot; value=\&quot;外部入口\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;932\&quot; y=\&quot;-240\&quot; width=\&quot;330\&quot; height=\&quot;128\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--5\&quot; value=\&quot;Web前端（浏览器/Jinja2+HTMX）\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;967\&quot; y=\&quot;-215\&quot; width=\&quot;260\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--6\&quot; value=\&quot;app/main.py&amp;#10;FastAPI主入口\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;1017\&quot; y=\&quot;-14\&quot; width=\&quot;160\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--7\&quot; value=\&quot;httpd/routers/&amp;#10;API路由\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;1013\&quot; y=\&quot;136\&quot; width=\&quot;167\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--8\&quot; value=\&quot;httpd/database/&amp;#10;数据库\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;715\&quot; y=\&quot;312\&quot; width=\&quot;181\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--9\&quot; value=\&quot;httpd/middlewares/&amp;#10;中间件\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;945\&quot; y=\&quot;312\&quot; width=\&quot;207\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--10\&quot; value=\&quot;httpd/store/&amp;#10;HTTP存储\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;1202\&quot; y=\&quot;312\&quot; width=\&quot;152\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--11\&quot; value=\&quot;woodenfish_mcp_host/host/host.py&amp;#10;woodenfishMcpHost\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;902\&quot; y=\&quot;572\&quot; width=\&quot;318\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--12\&quot; value=\&quot;woodenfish_mcp_host/models/__init__.py&amp;#10;模型加载\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;55\&quot; y=\&quot;747\&quot; width=\&quot;334\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--13\&quot; value=\&quot;woodenfish_mcp_host/host/tools/__init__.py&amp;#10;ToolManager\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;895\&quot; y=\&quot;1048\&quot; width=\&quot;356\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--14\&quot; value=\&quot;woodenfish_mcp_host/host/agents/chat_agent.py&amp;#10;ChatAgentFactory/StateGraph\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;2026\&quot; y=\&quot;898\&quot; width=\&quot;423\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--15\&quot; value=\&quot;woodenfish_mcp_host/host/chat.py&amp;#10;Chat会话\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;1829\&quot; y=\&quot;747\&quot; width=\&quot;319\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--16\&quot; value=\&quot;woodenfish_mcp_host/host/agents/tools_in_prompt.py&amp;#10;工具消息处理\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;2087\&quot; y=\&quot;1048\&quot; width=\&quot;460\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--17\&quot; value=\&quot;woodenfish_mcp_host/host/tools/各文件&amp;#10;MCP工具实现\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;919\&quot; y=\&quot;1199\&quot; width=\&quot;308\&quot; height=\&quot;102\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--18\&quot; value=\&quot;woodenfish_mcp_host/host/conf/llm.py&amp;#10;LLM配置\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;459\&quot; y=\&quot;747\&quot; width=\&quot;350\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--19\&quot; value=\&quot;woodenfish_mcp_host/host/helpers/context.py&amp;#10;上下文\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;859\&quot; y=\&quot;747\&quot; width=\&quot;403\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--20\&quot; value=\&quot;woodenfish_mcp_host/host/store/sqlite.py&amp;#10;状态存储\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;1496\&quot; y=\&quot;898\&quot; width=\&quot;372\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--21\&quot; value=\&quot;woodenfish_mcp_host/host/errors.py&amp;#10;异常处理\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;1312\&quot; y=\&quot;747\&quot; width=\&quot;330\&quot; height=\&quot;78\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--22\&quot; value=\&quot;各类MCP工具进程&amp;#10;如 tavily、sequential-thinking 等\&quot; style=\&quot;whiteSpace=wrap;strokeWidth=2;\&quot; parent=\&quot;1\&quot; vertex=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;943\&quot; y=\&quot;1423\&quot; width=\&quot;260\&quot; height=\&quot;102\&quot; as=\&quot;geometry\&quot;/&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--23\&quot; value=\&quot;HTTP请求\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=-0.01;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--5\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--6\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;/&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--24\&quot; value=\&quot;路由分发\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=0.99;entryX=0.5;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--6\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--7\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;/&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--25\&quot; value=\&quot;业务调用\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.72;entryX=0.5;entryY=0;rounded=0;entryDx=0;entryDy=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--7\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--11\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;920\&quot; y=\&quot;400\&quot;/&gt;&lt;mxPoint x=\&quot;1061\&quot; y=\&quot;510\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--26\&quot; value=\&quot;需要DB\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.78;entryX=0.5;entryY=-0.01;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--7\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--8\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;805\&quot; y=\&quot;250\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--27\&quot; value=\&quot;需要中间件\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.11;exitY=1;entryX=0.27;entryY=-0.01;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--7\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--9\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;1010\&quot; y=\&quot;250\&quot;/&gt;&lt;mxPoint x=\&quot;1020\&quot; y=\&quot;270\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--28\&quot; value=\&quot;需要存储\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.85;exitY=1;entryX=0.22;entryY=-0.01;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--7\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--10\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;1208\&quot; y=\&quot;250\&quot;/&gt;&lt;mxPoint x=\&quot;1208\&quot; y=\&quot;287\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--29\&quot; value=\&quot;加载模型\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.65;entryX=0.5;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--11\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--12\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;222\&quot; y=\&quot;675\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--30\&quot; value=\&quot;加载工具\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.7;entryX=0;entryY=0.24;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--11\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--13\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;424\&quot; y=\&quot;675\&quot;/&gt;&lt;mxPoint x=\&quot;424\&quot; y=\&quot;1012\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--31\&quot; value=\&quot;获取agent工厂\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.6;entryX=0.5;entryY=-0.01;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--11\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--14\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;2238\&quot; y=\&quot;675\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--32\&quot; value=\&quot;创建Chat会话\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.65;entryX=0.35;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--11\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--15\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;1870\&quot; y=\&quot;720\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--33\&quot; value=\&quot;消息流转\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.86;entryX=0.73;entryY=-0.01;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--15\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--14\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;2422\&quot; y=\&quot;861\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--34\&quot; value=\&quot;工具调用\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.25;exitY=0.99;entryX=1;entryY=0.32;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--14\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--13\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;2036\&quot; y=\&quot;1012\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--35\&quot; value=\&quot;调用具体工具\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--13\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--17\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;/&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--36\&quot; value=\&quot;工具消息处理\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.6;exitY=0.99;entryX=0.5;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--14\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--16\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;2317\&quot; y=\&quot;1012\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--37\&quot; value=\&quot;状态存储\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.67;entryX=0.642;entryY=-0.002;rounded=0;entryDx=0;entryDy=0;entryPerimeter=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--15\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--20\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;1720\&quot; y=\&quot;861\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--38\&quot; value=\&quot;配置/异常/辅助\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0;exitY=0.8;entryX=0.5;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--11\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--18\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;634\&quot; y=\&quot;675\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--39\&quot; value=\&quot;配置/异常/辅助\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=0.99;entryX=0.5;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--11\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--19\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;/&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--40\&quot; value=\&quot;配置/异常/辅助\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.81;entryX=0.5;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--11\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--21\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;1477\&quot; y=\&quot;675\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--41\&quot; value=\&quot;进程/协议调用\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.5;exitY=1;entryX=0.5;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--17\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--22\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;/&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--42\&quot; value=\&quot;业务调用\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=1;exitY=0.72;entryX=0.69;entryY=0;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--7\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--15\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry x=\&quot;0.161\&quot; y=\&quot;-23\&quot; relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;1710\&quot; y=\&quot;420\&quot;/&gt;&lt;mxPoint x=\&quot;1940\&quot; y=\&quot;630\&quot;/&gt;&lt;mxPoint x=\&quot;1960\&quot; y=\&quot;670\&quot;/&gt;&lt;/Array&gt;&lt;mxPoint as=\&quot;offset\&quot;/&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--44\&quot; value=\&quot;请求处理\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.64;exitY=-0.01;entryX=0.5;entryY=1;rounded=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--9\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--7\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;1097\&quot; y=\&quot;287\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;amgZ04lZu0PFt0ELvBa--45\&quot; value=\&quot;文件/缓存/临时存储\&quot; style=\&quot;curved=1;startArrow=none;endArrow=block;exitX=0.76;exitY=-0.01;entryX=1;entryY=0.5;rounded=0;entryDx=0;entryDy=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--10\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--7\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;1343\&quot; y=\&quot;287\&quot;/&gt;&lt;mxPoint x=\&quot;1343\&quot; y=\&quot;250\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;PRPnnYZUdqjaDxwN3JCB-2\&quot; value=\&quot;\&quot; style=\&quot;curved=1;endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;\&quot; parent=\&quot;1\&quot; source=\&quot;amgZ04lZu0PFt0ELvBa--8\&quot; target=\&quot;amgZ04lZu0PFt0ELvBa--20\&quot; edge=\&quot;1\&quot;&gt;&lt;mxGeometry width=\&quot;50\&quot; height=\&quot;50\&quot; relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;mxPoint x=\&quot;1840\&quot; y=\&quot;490\&quot; as=\&quot;sourcePoint\&quot;/&gt;&lt;mxPoint x=\&quot;1890\&quot; y=\&quot;440\&quot; as=\&quot;targetPoint\&quot;/&gt;&lt;Array as=\&quot;points\&quot;&gt;&lt;mxPoint x=\&quot;1480\&quot; y=\&quot;520\&quot;/&gt;&lt;mxPoint x=\&quot;1630\&quot; y=\&quot;580\&quot;/&gt;&lt;/Array&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;mxCell id=\&quot;PRPnnYZUdqjaDxwN3JCB-3\&quot; value=\&quot;&amp;lt;div style=&amp;quot;color: rgb(59, 59, 59); font-family: Menlo, Monaco, &amp;amp;quot;Courier New&amp;amp;quot;, monospace; font-size: 12px; line-height: 18px; white-space-collapse: preserve;&amp;quot;&amp;gt;状态/消息/历史&amp;lt;/div&amp;gt;\&quot; style=\&quot;edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];\&quot; parent=\&quot;PRPnnYZUdqjaDxwN3JCB-2\&quot; vertex=\&quot;1\&quot; connectable=\&quot;0\&quot;&gt;&lt;mxGeometry x=\&quot;-0.2394\&quot; relative=\&quot;1\&quot; as=\&quot;geometry\&quot;&gt;&lt;mxPoint as=\&quot;offset\&quot;/&gt;&lt;/mxGeometry&gt;&lt;/mxCell&gt;&lt;/root&gt;&lt;/mxGraphModel&gt;&lt;/diagram&gt;&lt;/mxfile&gt;&quot;,&quot;toolbar&quot;:&quot;pages zoom layers lightbox&quot;,&quot;page&quot;:0}"></div>
<script type="text/javascript" src="https://app.diagrams.net/js/viewer-static.min.js"></script>
</body>
</html>
