# woodenfish 主流程详细架构mermaid 流程图

> 本流程图详细反映新版 woodenfish 平台的主流程架构，兼顾 Web 前端、FastAPI 后端、智能体/工具链、API/DB/中间件/存储等主要模块，箭头和细节模仿原有详细风格。

```mermaid
flowchart TD
    %% 外部入口
    subgraph 外部入口
        A1[Web前端（浏览器/Jinja2+HTMX）]
    end

    %% FastAPI主服务
    subgraph FastAPI后端
        B1[app/main.py<br>FastAPI主入口]
        B2[httpd/routers/<br>API路由]
        B3[httpd/database/<br>数据库]
        B4[httpd/middlewares/<br>中间件]
        B5[httpd/store/<br>HTTP存储]
    end

    %% 智能体/工具链核心
    subgraph 智能体/工具链核心
        C1[woodenfish_mcp_host/host/host.py<br>woodenfishMcpHost]
        C2[woodenfish_mcp_host/models/__init__.py<br>模型加载]
        C3[woodenfish_mcp_host/host/tools/__init__.py<br>ToolManager]
        C4[woodenfish_mcp_host/host/agents/chat_agent.py<br>ChatAgentFactory/StateGraph]
        C5[woodenfish_mcp_host/host/chat.py<br>Chat会话]
        C6[woodenfish_mcp_host/host/agents/tools_in_prompt.py<br>工具消息处理]
        C7[具体的MCP工具实现<br>(e.g., host/tools/echo.py)]
        C8[woodenfish_mcp_host/host/conf/llm.py<br>LLM配置]
        C9[woodenfish_mcp_host/host/helpers/context.py<br>上下文管理协议]
        C10[woodenfish_mcp_host/host/store/sqlite.py<br>状态存储占位符]
        C11[woodenfish_mcp_host/host/errors.py<br>异常处理]
    end

    %% 真实MCP工具进程
    subgraph 真实MCP工具进程
        D1[独立的工具进程<br>(e.g., python echo.py)]
    end

    %% API/DB/中间件/存储
    subgraph API/DB/中间件/存储
        B27a[woodenfish_mcp_host/host/tools/mcp_server.py<br>McpServer]
        B27b[woodenfish_mcp_host/host/tools/stdio_server.py<br>StdioServer]
    end

    %% 连接关系
    A1 -- HTTP请求 --> B1
    B1 -- 路由分发 --> B2
    B2 -- 业务调用 --> C1
    B2 -- 需要DB --> B3
    B2 -- 需要中间件 --> B4
    B2 -- 需要存储 --> B5

    %% FastAPI主服务与智能体/工具链核心
    C1 -- 加载模型 --> C2
    C1 -- 加载工具 --> C3
    C1 -- 获取agent工厂 --> C4
    C1 -- 创建Chat会话 --> C5
    C5 -- 消息流转 --> C4
    C4 -- 工具调用 --> C3
    C3 -- 调用具体工具 --> C7
    C4 -- 工具消息处理 --> C6
    C5 -- 状态存储 --> C10
    C1 -- 配置/异常/辅助 --> C8
    C1 -- 配置/异常/辅助 --> C9
    C1 -- 配置/异常/辅助 --> C11

    %% 工具链与真实MCP工具进程
    C7 -- 进程/协议调用 --> D1

    %% API路由与智能体/工具链核心
    B2 -- 业务调用 --> C5

    %% DB/中间件/存储与智能体/工具链核心
    B3 -- 状态/消息/历史 --> C10
    B4 -- 请求处理 --> B2
    B5 -- 文件/缓存/临时存储 --> B2
```

---
