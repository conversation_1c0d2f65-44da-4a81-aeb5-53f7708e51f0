{"5eeca119-dcff-4ae6-84fe-133447870cd4": {"file_id": "5eeca119-dcff-4ae6-84fe-133447870cd4", "original_name": "test.txt", "stored_name": "5eeca119-dcff-4ae6-84fe-133447870cd4.txt", "file_path": "uploads/files/5eeca119-dcff-4ae6-84fe-133447870cd4.txt", "file_size": 19, "file_type": "document", "mime_type": "text/plain", "upload_time": "2025-06-29T12:21:14.919712", "last_access": "2025-06-29T12:21:14.919716", "access_count": 0, "tags": [], "description": ""}, "ca609074-6e8a-4262-ac13-1b510ecf4bff": {"file_id": "ca609074-6e8a-4262-ac13-1b510ecf4bff", "original_name": "woodenfish 主流程详细架构.html", "stored_name": "ca609074-6e8a-4262-ac13-1b510ecf4bff.html", "file_path": "uploads/files/ca609074-6e8a-4262-ac13-1b510ecf4bff.html", "file_size": 23355, "file_type": "code", "mime_type": "text/html", "upload_time": "2025-06-29T12:36:24.255184", "last_access": "2025-06-29T12:36:24.255194", "access_count": 0, "tags": [], "description": ""}, "e8a13624-e9c5-4aed-a541-51a1d46f0b1a": {"file_id": "e8a13624-e9c5-4aed-a541-51a1d46f0b1a", "original_name": "01_woodenfish主流程详细架构mermaid 流程图.md", "stored_name": "e8a13624-e9c5-4aed-a541-51a1d46f0b1a.md", "file_path": "uploads/files/e8a13624-e9c5-4aed-a541-51a1d46f0b1a.md", "file_size": 2882, "file_type": "document", "mime_type": "text/markdown", "upload_time": "2025-06-29T14:20:05.338472", "last_access": "2025-06-29T14:20:45.756678", "access_count": 1, "tags": [], "description": ""}}