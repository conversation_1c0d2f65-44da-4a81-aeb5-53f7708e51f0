"""
文件存储管理服务
处理文件上传、存储、清理等逻辑
"""

import os
import uuid
import shutil
import mimetypes
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import json
import asyncio
from dataclasses import dataclass, asdict
import hashlib

@dataclass
class FileMetadata:
    """文件元数据"""
    file_id: str
    original_name: str
    stored_name: str
    file_path: str
    file_size: int
    file_type: str
    mime_type: str
    upload_time: datetime
    last_access: datetime
    access_count: int = 0
    tags: List[str] = None
    description: str = ""

    def __post_init__(self):
        if self.tags is None:
            self.tags = []

class FileStorageManager:
    """文件存储管理器"""
    
    def __init__(self, base_dir: str = "uploads", metadata_file: str = "file_metadata.json"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        self.temp_dir = self.base_dir / "temp"
        self.permanent_dir = self.base_dir / "files"
        self.thumbnails_dir = self.base_dir / "thumbnails"
        
        for dir_path in [self.temp_dir, self.permanent_dir, self.thumbnails_dir]:
            dir_path.mkdir(exist_ok=True)
        
        self.metadata_file = self.base_dir / metadata_file
        self.metadata: Dict[str, FileMetadata] = {}
        self.load_metadata()
        
        # 配置
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        self.temp_file_lifetime = timedelta(hours=24)  # 临时文件保留24小时
        self.cleanup_interval = timedelta(hours=6)  # 每6小时清理一次
        
        # 支持的文件类型
        self.allowed_extensions = {
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg',
            '.pdf', '.doc', '.docx', '.txt', '.md', '.rtf',
            '.xls', '.xlsx', '.ppt', '.pptx',
            '.py', '.js', '.html', '.css', '.json', '.xml', '.yaml', '.yml',
            '.java', '.cpp', '.c', '.h', '.cs', '.php', '.rb', '.go', '.rs',
            '.zip', '.rar', '.7z', '.tar', '.gz',
            '.mp3', '.wav', '.mp4', '.avi', '.mov', '.mkv'
        }

    def load_metadata(self):
        """加载文件元数据"""
        try:
            if self.metadata_file.exists():
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for file_id, meta_dict in data.items():
                        # 转换日期字符串为datetime对象
                        meta_dict['upload_time'] = datetime.fromisoformat(meta_dict['upload_time'])
                        meta_dict['last_access'] = datetime.fromisoformat(meta_dict['last_access'])
                        self.metadata[file_id] = FileMetadata(**meta_dict)
        except Exception as e:
            print(f"加载文件元数据失败: {e}")
            self.metadata = {}

    def save_metadata(self):
        """保存文件元数据"""
        try:
            data = {}
            for file_id, metadata in self.metadata.items():
                meta_dict = asdict(metadata)
                # 转换datetime对象为字符串
                meta_dict['upload_time'] = metadata.upload_time.isoformat()
                meta_dict['last_access'] = metadata.last_access.isoformat()
                data[file_id] = meta_dict
            
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存文件元数据失败: {e}")

    def get_file_type(self, filename: str) -> str:
        """获取文件类型分类"""
        ext = Path(filename).suffix.lower()
        
        if ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']:
            return 'image'
        elif ext in ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf']:
            return 'document'
        elif ext in ['.xls', '.xlsx', '.ppt', '.pptx']:
            return 'spreadsheet'
        elif ext in ['.py', '.js', '.html', '.css', '.json', '.xml', '.yaml', '.yml', '.java', '.cpp', '.c', '.h', '.cs', '.php', '.rb', '.go', '.rs']:
            return 'code'
        elif ext in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return 'archive'
        elif ext in ['.mp3', '.wav']:
            return 'audio'
        elif ext in ['.mp4', '.avi', '.mov', '.mkv']:
            return 'video'
        else:
            return 'other'

    def validate_file(self, filename: str, file_size: int) -> Tuple[bool, str]:
        """验证文件"""
        if not filename:
            return False, "文件名不能为空"
        
        # 检查文件扩展名
        ext = Path(filename).suffix.lower()
        if ext not in self.allowed_extensions:
            return False, f"不支持的文件类型: {ext}"
        
        # 检查文件大小
        if file_size > self.max_file_size:
            return False, f"文件大小超过限制 ({self.max_file_size // (1024*1024)}MB)"
        
        return True, ""

    def generate_file_id(self) -> str:
        """生成唯一文件ID"""
        return str(uuid.uuid4())

    def get_stored_filename(self, file_id: str, original_name: str) -> str:
        """生成存储文件名"""
        ext = Path(original_name).suffix
        return f"{file_id}{ext}"

    async def save_file(self, file_content: bytes, original_name: str, 
                       temporary: bool = False, tags: List[str] = None, 
                       description: str = "") -> FileMetadata:
        """保存文件"""
        # 验证文件
        is_valid, error_msg = self.validate_file(original_name, len(file_content))
        if not is_valid:
            raise ValueError(error_msg)
        
        # 生成文件信息
        file_id = self.generate_file_id()
        stored_name = self.get_stored_filename(file_id, original_name)
        
        # 选择存储目录
        storage_dir = self.temp_dir if temporary else self.permanent_dir
        file_path = storage_dir / stored_name
        
        # 保存文件
        with open(file_path, 'wb') as f:
            f.write(file_content)
        
        # 创建元数据
        mime_type, _ = mimetypes.guess_type(original_name)
        metadata = FileMetadata(
            file_id=file_id,
            original_name=original_name,
            stored_name=stored_name,
            file_path=str(file_path),
            file_size=len(file_content),
            file_type=self.get_file_type(original_name),
            mime_type=mime_type or 'application/octet-stream',
            upload_time=datetime.now(),
            last_access=datetime.now(),
            tags=tags or [],
            description=description
        )
        
        # 保存元数据
        self.metadata[file_id] = metadata
        self.save_metadata()
        
        return metadata

    def get_file_metadata(self, file_id: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        metadata = self.metadata.get(file_id)
        if metadata:
            # 更新访问时间和次数
            metadata.last_access = datetime.now()
            metadata.access_count += 1
            self.save_metadata()
        return metadata

    def get_file_path(self, file_id: str) -> Optional[Path]:
        """获取文件路径"""
        metadata = self.get_file_metadata(file_id)
        if metadata and Path(metadata.file_path).exists():
            return Path(metadata.file_path)
        return None

    def delete_file(self, file_id: str) -> bool:
        """删除文件"""
        metadata = self.metadata.get(file_id)
        if not metadata:
            return False
        
        # 删除物理文件
        file_path = Path(metadata.file_path)
        if file_path.exists():
            file_path.unlink()
        
        # 删除缩略图（如果存在）
        thumbnail_path = self.thumbnails_dir / f"{file_id}.jpg"
        if thumbnail_path.exists():
            thumbnail_path.unlink()
        
        # 删除元数据
        del self.metadata[file_id]
        self.save_metadata()
        
        return True

    def list_files(self, file_type: Optional[str] = None, 
                  tags: Optional[List[str]] = None,
                  limit: int = 100) -> List[FileMetadata]:
        """列出文件"""
        files = list(self.metadata.values())
        
        # 按类型过滤
        if file_type:
            files = [f for f in files if f.file_type == file_type]
        
        # 按标签过滤
        if tags:
            files = [f for f in files if any(tag in f.tags for tag in tags)]
        
        # 按上传时间排序
        files.sort(key=lambda x: x.upload_time, reverse=True)
        
        return files[:limit]

    def cleanup_temp_files(self):
        """清理临时文件"""
        now = datetime.now()
        expired_files = []
        
        for file_id, metadata in self.metadata.items():
            # 检查是否为临时文件且已过期
            if (self.temp_dir.name in metadata.file_path and 
                now - metadata.upload_time > self.temp_file_lifetime):
                expired_files.append(file_id)
        
        # 删除过期文件
        for file_id in expired_files:
            self.delete_file(file_id)
        
        print(f"清理了 {len(expired_files)} 个过期临时文件")

    def get_storage_stats(self) -> Dict:
        """获取存储统计信息"""
        total_files = len(self.metadata)
        total_size = sum(meta.file_size for meta in self.metadata.values())
        
        # 按类型统计
        type_stats = {}
        for metadata in self.metadata.values():
            file_type = metadata.file_type
            if file_type not in type_stats:
                type_stats[file_type] = {'count': 0, 'size': 0}
            type_stats[file_type]['count'] += 1
            type_stats[file_type]['size'] += metadata.file_size
        
        return {
            'total_files': total_files,
            'total_size': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'type_stats': type_stats
        }

# 全局文件管理器实例
file_manager = FileStorageManager()
