"""
统一配置管理器
整合所有配置文件的加载和管理，提供统一的配置访问接口
"""

import json
import os
from typing import Dict, Any, Optional, Union
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_dir: 配置文件目录，默认为config目录
        """
        self.config_dir = Path(config_dir) if config_dir else Path("config")
        self._configs = {}
        self._load_all_configs()
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        config_files = {
            'llm': 'llm_config.json',
            'mcp': 'mcp_config.json', 
            'persistence': 'persistence_config.json'
        }
        
        for config_name, filename in config_files.items():
            config_path = self.config_dir / filename
            try:
                if config_path.exists():
                    with open(config_path, 'r', encoding='utf-8') as f:
                        self._configs[config_name] = json.load(f)
                    logger.info(f"已加载配置文件: {filename}")
                else:
                    logger.warning(f"配置文件不存在: {filename}")
                    self._configs[config_name] = {}
            except Exception as e:
                logger.error(f"加载配置文件 {filename} 失败: {e}")
                self._configs[config_name] = {}
    
    def get_llm_config(self, provider: Optional[str] = None) -> Dict[str, Any]:
        """
        获取LLM配置
        
        Args:
            provider: 指定提供商，如果为None则返回默认提供商配置
            
        Returns:
            LLM配置字典
        """
        llm_config = self._configs.get('llm', {})
        
        if not provider:
            provider = llm_config.get('default_provider', 'zhipu')
        
        models = llm_config.get('models', {})
        if provider in models:
            return models[provider]
        else:
            logger.warning(f"未找到提供商 {provider} 的配置，使用默认配置")
            default_provider = llm_config.get('default_provider', 'zhipu')
            return models.get(default_provider, {})
    
    def get_mcp_config(self) -> Dict[str, Any]:
        """获取MCP配置"""
        return self._configs.get('mcp', {})
    
    def get_mcp_servers(self) -> Dict[str, Any]:
        """获取MCP服务器配置"""
        return self.get_mcp_config().get('mcpServers', {})
    
    def get_persistence_config(self) -> Dict[str, Any]:
        """获取持久化配置"""
        # persistence_config.json的结构是 {"persistence": {...}}
        persistence_file_config = self._configs.get('persistence', {})
        return persistence_file_config.get('persistence', {})
    
    def get_session_config(self) -> Dict[str, Any]:
        """获取会话管理配置"""
        persistence_config = self.get_persistence_config()
        return persistence_config.get('session_management', {})
    
    def get_memory_config(self) -> Dict[str, Any]:
        """获取内存设置配置"""
        persistence_config = self.get_persistence_config()
        return persistence_config.get('memory_settings', {})
    
    def get_backup_config(self) -> Dict[str, Any]:
        """获取备份设置配置"""
        persistence_config = self.get_persistence_config()
        return persistence_config.get('backup_settings', {})
    
    def get_database_path(self) -> str:
        """获取数据库路径"""
        persistence_config = self.get_persistence_config()
        sqlite_config = persistence_config.get('config', {}).get('sqlite', {})
        return sqlite_config.get('database_path', './data/agent_memory.db')
    
    def is_persistence_enabled(self) -> bool:
        """检查是否启用持久化"""
        persistence_config = self.get_persistence_config()
        return persistence_config.get('enabled', True)
    
    def get_persistence_backend(self) -> str:
        """获取持久化后端类型"""
        persistence_config = self.get_persistence_config()
        return persistence_config.get('backend', 'sqlite')
    
    def get_config(self, config_name: str, key_path: Optional[str] = None) -> Any:
        """
        通用配置获取方法
        
        Args:
            config_name: 配置名称 ('llm', 'mcp', 'persistence')
            key_path: 配置键路径，用点分隔，如 'models.zhipu.api_key'
            
        Returns:
            配置值
        """
        config = self._configs.get(config_name, {})
        
        if not key_path:
            return config
        
        keys = key_path.split('.')
        current = config
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def set_config(self, config_name: str, key_path: str, value: Any):
        """
        设置配置值（仅在内存中，不保存到文件）
        
        Args:
            config_name: 配置名称
            key_path: 配置键路径
            value: 配置值
        """
        if config_name not in self._configs:
            self._configs[config_name] = {}
        
        keys = key_path.split('.')
        current = self._configs[config_name]
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def reload_config(self, config_name: Optional[str] = None):
        """
        重新加载配置
        
        Args:
            config_name: 指定重新加载的配置名称，如果为None则重新加载所有配置
        """
        if config_name:
            # 重新加载指定配置
            config_files = {
                'llm': 'llm_config.json',
                'mcp': 'mcp_config.json',
                'persistence': 'persistence_config.json'
            }
            
            if config_name in config_files:
                filename = config_files[config_name]
                config_path = self.config_dir / filename
                try:
                    if config_path.exists():
                        with open(config_path, 'r', encoding='utf-8') as f:
                            self._configs[config_name] = json.load(f)
                        logger.info(f"已重新加载配置文件: {filename}")
                    else:
                        logger.warning(f"配置文件不存在: {filename}")
                except Exception as e:
                    logger.error(f"重新加载配置文件 {filename} 失败: {e}")
        else:
            # 重新加载所有配置
            self._load_all_configs()
    
    def validate_config(self) -> Dict[str, bool]:
        """
        验证配置完整性
        
        Returns:
            验证结果字典
        """
        results = {}
        
        # 验证LLM配置
        llm_config = self.get_llm_config()
        results['llm'] = bool(llm_config.get('api_key') and llm_config.get('model_name'))
        
        # 验证MCP配置
        mcp_servers = self.get_mcp_servers()
        results['mcp'] = len(mcp_servers) > 0
        
        # 验证持久化配置
        persistence_config = self.get_persistence_config()
        results['persistence'] = bool(
            persistence_config.get('enabled') and
            persistence_config.get('backend')
        )
        
        return results
    
    def get_web_config(self) -> Dict[str, Any]:
        """
        获取Web API相关配置（可扩展）
        
        Returns:
            Web配置字典
        """
        # 这里可以添加Web API相关的配置
        # 目前返回默认配置
        return {
            'host': '0.0.0.0',
            'port': 8000,
            'debug': False,
            'cors_origins': ['*'],
            'max_request_size': 10 * 1024 * 1024,  # 10MB
            'timeout': 300,  # 5分钟
            'websocket_timeout': 3600,  # 1小时
        }


# 全局配置管理器实例
config_manager = ConfigManager()
