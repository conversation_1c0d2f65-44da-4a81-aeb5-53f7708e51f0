{"name": "@matechat/playground", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build-only": "vite build", "type-check": "vue-tsc --build", "preview": "vite preview"}, "dependencies": {"lodash-es": "^4.17.21", "vue": "^3.5.13", "vue-devui": "^1.6.31"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "vite": "^6.0.11", "vue-tsc": "^2.2.0"}}