{"name": "my-project-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "MateChat前端界面 - my_project AI智能助手系统的现代化前端", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@matechat/core": "^1.5.3", "@types/markdown-it": "^14.1.2", "@vueuse/core": "^13.4.0", "axios": "^1.10.0", "highlight.js": "^11.11.1", "markdown-it": "^14.1.0", "pinia": "^3.0.3", "vue": "^3.5.17"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "sass-embedded": "^1.89.2", "typescript": "~5.8.3", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}