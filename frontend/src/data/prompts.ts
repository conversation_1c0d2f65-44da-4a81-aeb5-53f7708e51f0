/**
 * 提示词数据结构和预设提示词
 */

export interface PromptItem {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  icon?: string;
  description?: string;
  variables?: PromptVariable[];
  isCustom?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  usageCount?: number;
}

export interface PromptVariable {
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'number';
  required?: boolean;
  placeholder?: string;
  options?: string[];
  defaultValue?: string;
}

export interface PromptCategory {
  id: string;
  name: string;
  icon: string;
  description: string;
  color?: string;
}

// 提示词分类
export const promptCategories: PromptCategory[] = [
  {
    id: 'writing',
    name: '写作助手',
    icon: '✍️',
    description: '文章写作、内容创作相关提示词',
    color: '#1890ff'
  },
  {
    id: 'coding',
    name: '编程开发',
    icon: '💻',
    description: '代码编写、调试、优化相关提示词',
    color: '#52c41a'
  },
  {
    id: 'analysis',
    name: '分析总结',
    icon: '📊',
    description: '数据分析、内容总结相关提示词',
    color: '#fa8c16'
  },
  {
    id: 'translation',
    name: '翻译润色',
    icon: '🌐',
    description: '语言翻译、文本润色相关提示词',
    color: '#722ed1'
  },
  {
    id: 'creative',
    name: '创意设计',
    icon: '🎨',
    description: '创意思考、设计灵感相关提示词',
    color: '#eb2f96'
  },
  {
    id: 'business',
    name: '商务办公',
    icon: '💼',
    description: '商务沟通、办公文档相关提示词',
    color: '#13c2c2'
  },
  {
    id: 'learning',
    name: '学习教育',
    icon: '📚',
    description: '学习方法、知识解答相关提示词',
    color: '#f5222d'
  },
  {
    id: 'custom',
    name: '自定义',
    icon: '⚙️',
    description: '用户自定义的提示词',
    color: '#666666'
  }
];

// 预设提示词
export const defaultPrompts: PromptItem[] = [
  // 写作助手
  {
    id: 'article_writer',
    title: '文章写作助手',
    content: '请帮我写一篇关于"{topic}"的文章，要求：\n1. 字数约{wordCount}字\n2. 风格：{style}\n3. 目标读者：{audience}\n4. 包含具体案例和数据支撑\n5. 结构清晰，逻辑严谨',
    category: 'writing',
    tags: ['文章', '写作', '内容创作'],
    icon: '📝',
    description: '帮助用户快速生成高质量文章',
    variables: [
      { name: 'topic', label: '文章主题', type: 'text', required: true, placeholder: '请输入文章主题' },
      { name: 'wordCount', label: '字数要求', type: 'select', options: ['500', '1000', '2000', '3000'], defaultValue: '1000' },
      { name: 'style', label: '写作风格', type: 'select', options: ['正式', '轻松', '学术', '科普'], defaultValue: '正式' },
      { name: 'audience', label: '目标读者', type: 'text', placeholder: '如：技术人员、普通用户等' }
    ]
  },
  {
    id: 'email_writer',
    title: '邮件写作',
    content: '请帮我写一封{type}邮件：\n收件人：{recipient}\n主题：{subject}\n主要内容：{content}\n\n要求语气{tone}，格式规范，内容简洁明了。',
    category: 'writing',
    tags: ['邮件', '商务沟通'],
    icon: '📧',
    description: '快速生成各类商务邮件',
    variables: [
      { name: 'type', label: '邮件类型', type: 'select', options: ['工作汇报', '请假申请', '会议邀请', '项目进展', '客户沟通'], required: true },
      { name: 'recipient', label: '收件人', type: 'text', required: true },
      { name: 'subject', label: '邮件主题', type: 'text', required: true },
      { name: 'content', label: '主要内容', type: 'textarea', required: true },
      { name: 'tone', label: '语气', type: 'select', options: ['正式', '友好', '紧急', '感谢'], defaultValue: '正式' }
    ]
  },

  // 编程开发
  {
    id: 'code_review',
    title: '代码审查',
    content: '请对以下{language}代码进行详细审查：\n\n```{language}\n{code}\n```\n\n请从以下方面进行分析：\n1. 代码质量和规范性\n2. 性能优化建议\n3. 安全性问题\n4. 可维护性改进\n5. 最佳实践建议',
    category: 'coding',
    tags: ['代码审查', '优化', '最佳实践'],
    icon: '🔍',
    description: '专业的代码审查和优化建议',
    variables: [
      { name: 'language', label: '编程语言', type: 'select', options: ['JavaScript', 'Python', 'Java', 'C++', 'Go', 'Rust'], required: true },
      { name: 'code', label: '代码内容', type: 'textarea', required: true, placeholder: '请粘贴需要审查的代码' }
    ]
  },
  {
    id: 'bug_debugger',
    title: 'Bug调试助手',
    content: '我遇到了一个{language}程序的bug，请帮我分析和解决：\n\n**错误描述：**\n{errorDescription}\n\n**错误信息：**\n```\n{errorMessage}\n```\n\n**相关代码：**\n```{language}\n{code}\n```\n\n**运行环境：**\n{environment}\n\n请提供详细的问题分析和解决方案。',
    category: 'coding',
    tags: ['调试', 'bug修复', '问题解决'],
    icon: '🐛',
    description: '快速定位和解决代码问题',
    variables: [
      { name: 'language', label: '编程语言', type: 'select', options: ['JavaScript', 'Python', 'Java', 'C++', 'Go', 'Rust'], required: true },
      { name: 'errorDescription', label: '错误描述', type: 'textarea', required: true },
      { name: 'errorMessage', label: '错误信息', type: 'textarea', required: true },
      { name: 'code', label: '相关代码', type: 'textarea', required: true },
      { name: 'environment', label: '运行环境', type: 'text', placeholder: '如：Node.js 18, Python 3.9等' }
    ]
  },

  // 分析总结
  {
    id: 'data_analyzer',
    title: '数据分析师',
    content: '请对以下数据进行专业分析：\n\n**数据类型：**{dataType}\n**分析目标：**{objective}\n**数据内容：**\n{data}\n\n请提供：\n1. 数据概览和基本统计\n2. 关键趋势和模式识别\n3. 异常值分析\n4. 结论和建议\n5. 可视化建议',
    category: 'analysis',
    tags: ['数据分析', '统计', '趋势分析'],
    icon: '📈',
    description: '专业的数据分析和洞察',
    variables: [
      { name: 'dataType', label: '数据类型', type: 'select', options: ['销售数据', '用户行为', '财务数据', '市场调研', '其他'], required: true },
      { name: 'objective', label: '分析目标', type: 'text', required: true },
      { name: 'data', label: '数据内容', type: 'textarea', required: true, placeholder: '请粘贴数据或描述数据结构' }
    ]
  },
  {
    id: 'content_summarizer',
    title: '内容总结',
    content: '请对以下内容进行{summaryType}总结：\n\n{content}\n\n要求：\n1. 提取核心要点\n2. 保持逻辑清晰\n3. 字数控制在{wordLimit}字以内\n4. 突出重要信息',
    category: 'analysis',
    tags: ['总结', '提炼', '要点'],
    icon: '📋',
    description: '快速提取内容核心要点',
    variables: [
      { name: 'summaryType', label: '总结类型', type: 'select', options: ['简要总结', '详细总结', '要点提取', '结构化总结'], defaultValue: '简要总结' },
      { name: 'content', label: '原始内容', type: 'textarea', required: true },
      { name: 'wordLimit', label: '字数限制', type: 'select', options: ['100', '200', '300', '500'], defaultValue: '200' }
    ]
  },

  // 翻译润色
  {
    id: 'translator',
    title: '专业翻译',
    content: '请将以下{sourceLanguage}文本翻译成{targetLanguage}：\n\n{text}\n\n翻译要求：\n1. 准确传达原意\n2. 符合{targetLanguage}表达习惯\n3. 保持{style}风格\n4. 注意专业术语的准确性',
    category: 'translation',
    tags: ['翻译', '多语言'],
    icon: '🌍',
    description: '高质量的多语言翻译服务',
    variables: [
      { name: 'sourceLanguage', label: '源语言', type: 'select', options: ['中文', '英文', '日文', '韩文', '法文', '德文'], required: true },
      { name: 'targetLanguage', label: '目标语言', type: 'select', options: ['中文', '英文', '日文', '韩文', '法文', '德文'], required: true },
      { name: 'text', label: '待翻译文本', type: 'textarea', required: true },
      { name: 'style', label: '翻译风格', type: 'select', options: ['正式', '口语化', '学术', '商务'], defaultValue: '正式' }
    ]
  },

  // 创意设计
  {
    id: 'creative_brainstorm',
    title: '创意头脑风暴',
    content: '我需要为"{project}"项目进行创意头脑风暴，请帮我：\n\n**项目类型：**{projectType}\n**目标受众：**{audience}\n**核心需求：**{requirements}\n**限制条件：**{constraints}\n\n请提供：\n1. 10个创新想法\n2. 每个想法的简要说明\n3. 可行性分析\n4. 推荐的前3个方案\n5. 实施建议',
    category: 'creative',
    tags: ['创意', '头脑风暴', '方案设计'],
    icon: '💡',
    description: '激发创意思维，提供创新方案',
    variables: [
      { name: 'project', label: '项目名称', type: 'text', required: true },
      { name: 'projectType', label: '项目类型', type: 'select', options: ['产品设计', '营销活动', '内容创作', '用户体验', '其他'], required: true },
      { name: 'audience', label: '目标受众', type: 'text', required: true },
      { name: 'requirements', label: '核心需求', type: 'textarea', required: true },
      { name: 'constraints', label: '限制条件', type: 'textarea', placeholder: '如：预算、时间、技术限制等' }
    ]
  },

  // 学习教育
  {
    id: 'concept_explainer',
    title: '概念解释器',
    content: '请用{level}的方式解释"{concept}"这个概念：\n\n要求：\n1. 定义清晰准确\n2. 提供具体例子\n3. 说明应用场景\n4. 与相关概念的区别\n5. 学习建议\n\n如果涉及{field}领域的专业知识，请特别注意准确性。',
    category: 'learning',
    tags: ['概念解释', '学习', '教育'],
    icon: '🎓',
    description: '深入浅出地解释复杂概念',
    variables: [
      { name: 'concept', label: '概念名称', type: 'text', required: true },
      { name: 'level', label: '解释水平', type: 'select', options: ['小学生能理解', '中学生水平', '大学生水平', '专业人士水平'], defaultValue: '大学生水平' },
      { name: 'field', label: '所属领域', type: 'text', placeholder: '如：计算机科学、物理学、经济学等' }
    ]
  }
];

// 常用快捷提示词（不需要变量）
export const quickPrompts: PromptItem[] = [
  {
    id: 'explain_simple',
    title: '简单解释',
    content: '请用简单易懂的语言解释一下：',
    category: 'learning',
    tags: ['解释', '简化'],
    icon: '💭'
  },
  {
    id: 'pros_cons',
    title: '优缺点分析',
    content: '请分析一下以下内容的优点和缺点：',
    category: 'analysis',
    tags: ['分析', '对比'],
    icon: '⚖️'
  },
  {
    id: 'step_by_step',
    title: '步骤指导',
    content: '请提供详细的步骤指导：',
    category: 'learning',
    tags: ['指导', '步骤'],
    icon: '📝'
  },
  {
    id: 'improve_text',
    title: '文本改进',
    content: '请帮我改进以下文本，使其更加清晰、准确、有说服力：',
    category: 'writing',
    tags: ['改进', '润色'],
    icon: '✨'
  },
  {
    id: 'generate_ideas',
    title: '想法生成',
    content: '请为以下主题生成一些创新的想法和建议：',
    category: 'creative',
    tags: ['创意', '想法'],
    icon: '🚀'
  }
];

// 提示词管理工具函数
export class PromptManager {
  private static readonly STORAGE_KEY = 'chat_prompts';
  private static readonly CUSTOM_PROMPTS_KEY = 'custom_prompts';

  static getAllPrompts(): PromptItem[] {
    const customPrompts = this.getCustomPrompts();
    return [...defaultPrompts, ...quickPrompts, ...customPrompts];
  }

  static getPromptsByCategory(categoryId: string): PromptItem[] {
    return this.getAllPrompts().filter(prompt => prompt.category === categoryId);
  }

  static getCustomPrompts(): PromptItem[] {
    try {
      const stored = localStorage.getItem(this.CUSTOM_PROMPTS_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  static saveCustomPrompt(prompt: Omit<PromptItem, 'id' | 'isCustom' | 'createdAt'>): PromptItem {
    const newPrompt: PromptItem = {
      ...prompt,
      id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      isCustom: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      usageCount: 0
    };

    const customPrompts = this.getCustomPrompts();
    customPrompts.push(newPrompt);
    localStorage.setItem(this.CUSTOM_PROMPTS_KEY, JSON.stringify(customPrompts));
    
    return newPrompt;
  }

  static updatePrompt(id: string, updates: Partial<PromptItem>): boolean {
    const customPrompts = this.getCustomPrompts();
    const index = customPrompts.findIndex(p => p.id === id);
    
    if (index !== -1) {
      customPrompts[index] = {
        ...customPrompts[index],
        ...updates,
        updatedAt: new Date()
      };
      localStorage.setItem(this.CUSTOM_PROMPTS_KEY, JSON.stringify(customPrompts));
      return true;
    }
    return false;
  }

  static deletePrompt(id: string): boolean {
    const customPrompts = this.getCustomPrompts();
    const filteredPrompts = customPrompts.filter(p => p.id !== id);
    
    if (filteredPrompts.length !== customPrompts.length) {
      localStorage.setItem(this.CUSTOM_PROMPTS_KEY, JSON.stringify(filteredPrompts));
      return true;
    }
    return false;
  }

  static incrementUsage(id: string): void {
    const customPrompts = this.getCustomPrompts();
    const prompt = customPrompts.find(p => p.id === id);
    
    if (prompt) {
      prompt.usageCount = (prompt.usageCount || 0) + 1;
      localStorage.setItem(this.CUSTOM_PROMPTS_KEY, JSON.stringify(customPrompts));
    }
  }

  static searchPrompts(query: string): PromptItem[] {
    const allPrompts = this.getAllPrompts();
    const lowercaseQuery = query.toLowerCase();
    
    return allPrompts.filter(prompt => 
      prompt.title.toLowerCase().includes(lowercaseQuery) ||
      prompt.content.toLowerCase().includes(lowercaseQuery) ||
      prompt.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
      (prompt.description && prompt.description.toLowerCase().includes(lowercaseQuery))
    );
  }
}
