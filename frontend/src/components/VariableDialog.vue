<template>
  <div class="variable-dialog-overlay" @click="handleOverlayClick">
    <div class="variable-dialog" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">配置提示词变量</h3>
        <button @click="$emit('cancel')" class="close-btn">✕</button>
      </div>

      <div class="dialog-content">
        <div class="prompt-info">
          <div class="prompt-title">{{ prompt?.title }}</div>
          <div class="prompt-description" v-if="prompt?.description">
            {{ prompt.description }}
          </div>
        </div>

        <div class="variables-form">
          <div
            v-for="variable in prompt?.variables"
            :key="variable.name"
            class="variable-field"
          >
            <label class="variable-label">
              {{ variable.label }}
              <span v-if="variable.required" class="required">*</span>
            </label>

            <!-- 文本输入 -->
            <input
              v-if="variable.type === 'text'"
              v-model="variableValues[variable.name]"
              :placeholder="variable.placeholder"
              :required="variable.required"
              class="variable-input"
              type="text"
            />

            <!-- 多行文本 -->
            <textarea
              v-else-if="variable.type === 'textarea'"
              v-model="variableValues[variable.name]"
              :placeholder="variable.placeholder"
              :required="variable.required"
              class="variable-textarea"
              rows="3"
            ></textarea>

            <!-- 选择框 -->
            <select
              v-else-if="variable.type === 'select'"
              v-model="variableValues[variable.name]"
              :required="variable.required"
              class="variable-select"
            >
              <option value="">请选择...</option>
              <option
                v-for="option in variable.options"
                :key="option"
                :value="option"
              >
                {{ option }}
              </option>
            </select>

            <!-- 数字输入 -->
            <input
              v-else-if="variable.type === 'number'"
              v-model.number="variableValues[variable.name]"
              :placeholder="variable.placeholder"
              :required="variable.required"
              class="variable-input"
              type="number"
            />
          </div>
        </div>

        <!-- 预览区域 -->
        <div class="preview-section">
          <h4 class="preview-title">预览效果</h4>
          <div class="preview-content">
            {{ previewContent }}
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button @click="$emit('cancel')" class="cancel-btn">
          取消
        </button>
        <button @click="handleConfirm" class="confirm-btn" :disabled="!isValid">
          确认使用
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import type { PromptItem } from '@/data/prompts';

interface Props {
  prompt: PromptItem | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  confirm: [content: string];
  cancel: [];
}>();

// 响应式数据
const variableValues = ref<Record<string, any>>({});

// 计算属性
const isValid = computed(() => {
  if (!props.prompt?.variables) return true;
  
  return props.prompt.variables.every(variable => {
    if (!variable.required) return true;
    const value = variableValues.value[variable.name];
    return value !== undefined && value !== null && value !== '';
  });
});

const previewContent = computed(() => {
  if (!props.prompt) return '';
  
  let content = props.prompt.content;
  
  // 替换变量占位符
  if (props.prompt.variables) {
    props.prompt.variables.forEach(variable => {
      const value = variableValues.value[variable.name] || `{${variable.name}}`;
      const regex = new RegExp(`\\{${variable.name}\\}`, 'g');
      content = content.replace(regex, value);
    });
  }
  
  return content;
});

// 方法
const handleOverlayClick = () => {
  emit('cancel');
};

const handleConfirm = () => {
  if (isValid.value) {
    emit('confirm', previewContent.value);
  }
};

const initializeValues = () => {
  if (!props.prompt?.variables) return;
  
  const values: Record<string, any> = {};
  props.prompt.variables.forEach(variable => {
    values[variable.name] = variable.defaultValue || '';
  });
  variableValues.value = values;
};

// 监听prompt变化
watch(() => props.prompt, (newPrompt) => {
  if (newPrompt) {
    initializeValues();
  }
}, { immediate: true });

// 生命周期
onMounted(() => {
  initializeValues();
});
</script>

<style scoped>
.variable-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.variable-dialog {
  background: white;
  border-radius: 8px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.close-btn:hover {
  background: #f0f0f0;
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.prompt-info {
  margin-bottom: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.prompt-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.prompt-description {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.variables-form {
  margin-bottom: 20px;
}

.variable-field {
  margin-bottom: 16px;
}

.variable-label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  font-size: 14px;
}

.required {
  color: #ff4d4f;
}

.variable-input,
.variable-textarea,
.variable-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.variable-input:focus,
.variable-textarea:focus,
.variable-select:focus {
  border-color: #1890ff;
}

.variable-textarea {
  resize: vertical;
  min-height: 60px;
}

.preview-section {
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
}

.preview-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.preview-content {
  background: #f8f9fa;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
}

.cancel-btn,
.confirm-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.cancel-btn {
  background: white;
  color: #666;
}

.cancel-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.confirm-btn {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.confirm-btn:hover:not(:disabled) {
  background: #40a9ff;
}

.confirm-btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  border-color: #e8e8e8;
  cursor: not-allowed;
}
</style>
