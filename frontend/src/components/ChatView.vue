<template>
  <div class="chat-view-wrapper">
    <div class="chat-view-container">
      <!-- 历史记录侧边栏 -->
      <HistoryList v-if="showHistory" />
      
      <!-- 主聊天区域 -->
      <div class="main-chat-area">
        <!-- 顶部导航 -->
        <NavbarTop />
        
        <!-- 聊天内容区域 -->
        <ChatProcess v-if="chatStore.startChat" />
        <Welcome v-else />
        
        <!-- 底部输入区域 -->
        <ChatInput />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useChatStore } from '@/stores/chat';
import HistoryList from './HistoryList.vue';
import NavbarTop from './NavbarTop.vue';
import ChatProcess from './ChatProcess.vue';
import Welcome from './Welcome.vue';
import ChatInput from './ChatInput.vue';

const chatStore = useChatStore();

// 控制历史记录显示
const showHistory = ref(true);
</script>

<style scoped lang="scss">
.chat-view-wrapper {
  position: relative;
  display: flex;
  width: 100vw;
  height: 100vh;
  background: var(--devui-global-bg, #f8f9fa);
}

.chat-view-container {
  display: flex;
  width: 100%;
  height: 100%;
}

.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 0;
}
</style>
