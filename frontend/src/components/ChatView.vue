<template>
  <div class="chat-view-wrapper">
    <div class="chat-view-container">
      <!-- 历史记录侧边栏 -->
      <HistoryList v-if="showHistory" />
      
      <!-- 主聊天区域 -->
      <div class="main-chat-area">
        <!-- 顶部导航 -->
        <NavbarTop />
        
        <!-- 聊天内容区域 -->
        <ChatProcess v-if="chatStore.startChat" />
        <Welcome v-else />
        
        <!-- 底部输入区域 -->
        <ChatInput />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useChatStore } from '@/stores/chat';
import HistoryList from './HistoryList.vue';
import NavbarTop from './NavbarTop.vue';
import ChatProcess from './ChatProcess.vue';
import Welcome from './Welcome.vue';
import ChatInput from './ChatInput.vue';

const chatStore = useChatStore();

// 控制历史记录显示
const showHistory = ref(true);

// 组件挂载时初始化
onMounted(() => {
  // 初始化store（加载会话数据）
  chatStore.initializeStore();

  // 建立WebSocket连接以检测连接状态
  chatStore.connectWebSocket();
});

// 组件卸载时清理连接
onUnmounted(() => {
  chatStore.disconnectWebSocket();
});
</script>

<style scoped lang="scss">
.chat-view-wrapper {
  position: relative;
  display: flex;
  width: 100%;
  height: 100vh;
  background: var(--devui-global-bg, #f8f9fa);
  overflow: hidden;
}

.chat-view-container {
  display: flex;
  width: 100%;
  height: 100%;
  max-width: 1400px; // 限制最大宽度
  margin: 0 auto; // 居中显示
}

.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 0;
  max-width: 1000px; // 主聊天区域最大宽度
  margin: 0 auto; // 居中显示
}

// 响应式设计
@media (max-width: 1200px) {
  .chat-view-container {
    max-width: 100%;
    padding: 0 20px;
  }

  .main-chat-area {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .chat-view-container {
    padding: 0 10px;
  }
}
</style>
