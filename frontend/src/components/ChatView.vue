<template>
  <div class="chat-view-wrapper">
    <div class="chat-view-container">
      <!-- 左侧会话历史面板 - 可折叠 -->
      <div class="sidebar-container" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-header">
          <div class="sidebar-title">
            <img src="https://matechat.gitcode.com/logo.svg" alt="MateChat" class="logo" />
            <span v-if="!sidebarCollapsed" class="title-text">对话历史</span>
          </div>
          <button
            class="collapse-btn"
            @click="toggleSidebar"
            :title="sidebarCollapsed ? '展开侧边栏' : '折叠侧边栏'"
          >
            <span v-if="sidebarCollapsed">→</span>
            <span v-else>←</span>
          </button>
        </div>

        <div class="sidebar-content" v-if="!sidebarCollapsed">
          <HistoryList />
        </div>
      </div>

      <!-- 主聊天区域 -->
      <div class="main-chat-area">
        <!-- 顶部导航 -->
        <NavbarTop />

        <!-- 聊天内容区域 -->
        <ChatProcess v-if="chatStore.startChat" />
        <Welcome v-else />

        <!-- 底部输入区域 -->
        <ChatInput />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useChatStore } from '@/stores/chat';
import HistoryList from './HistoryList.vue';
import NavbarTop from './NavbarTop.vue';
import ChatProcess from './ChatProcess.vue';
import Welcome from './Welcome.vue';
import ChatInput from './ChatInput.vue';

const chatStore = useChatStore();

// 控制侧边栏折叠状态
const sidebarCollapsed = ref(false);

// 切换侧边栏状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
  // 保存状态到localStorage
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString());
};

// 从localStorage恢复侧边栏状态
const restoreSidebarState = () => {
  const saved = localStorage.getItem('sidebarCollapsed');
  if (saved !== null) {
    sidebarCollapsed.value = saved === 'true';
  }
};

// 组件挂载时初始化
onMounted(() => {
  // 恢复侧边栏状态
  restoreSidebarState();

  // 初始化store（加载会话数据）
  chatStore.initializeStore();

  // 建立WebSocket连接以检测连接状态
  chatStore.connectWebSocket();
});

// 组件卸载时清理连接
onUnmounted(() => {
  chatStore.disconnectWebSocket();
});
</script>

<style scoped lang="scss">
.chat-view-wrapper {
  position: relative;
  display: flex;
  width: 100%;
  height: 100vh;
  background: var(--devui-global-bg, #f8f9fa);
  overflow: hidden;
}

.chat-view-container {
  display: flex;
  width: 100%;
  height: 100%;
  // 移除最大宽度限制，让布局填满整个浏览器
}

// 左侧边栏样式
.sidebar-container {
  width: 280px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-right: 1px solid var(--devui-dividing-line, #ddd);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;

  &.collapsed {
    width: 60px;
  }

  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .sidebar-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: white;
      font-weight: 600;

      .logo {
        width: 24px;
        height: 24px;
        filter: brightness(0) invert(1); // 让logo变白色
      }

      .title-text {
        font-size: 16px;
        white-space: nowrap;
      }
    }

    .collapse-btn {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      color: white;
      width: 28px;
      height: 28px;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      transition: background-color 0.2s;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow: hidden;
  }
}

// 主聊天区域样式
.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 0;
  background: var(--devui-base-bg, white);
}

// 响应式设计
@media (max-width: 1024px) {
  .sidebar-container {
    width: 240px;

    &.collapsed {
      width: 50px;
    }
  }
}

@media (max-width: 768px) {
  .sidebar-container {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

    &.collapsed {
      transform: translateX(-100%);
      width: 280px; // 移动端折叠时完全隐藏
    }
  }

  .main-chat-area {
    width: 100%;
  }
}
</style>
