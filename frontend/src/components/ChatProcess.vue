<template>
  <div ref="conversationRef" class="conversation-area">
    <div class="chat-list">
      <template v-for="(msg, idx) in messages" :key="idx">
        <!-- 用户消息 -->
        <McBubble
          v-if="msg.role === 'user'"
          :content="msg.content"
          :align="'right'"
          :avatarConfig="{ imgSrc: 'https://matechat.gitcode.com/png/demo/userAvatar.svg' }"
        />
        
        <!-- AI消息 -->
        <McBubble
          v-else
          :loading="msg.loading ?? false"
          :avatarConfig="{ imgSrc: 'https://matechat.gitcode.com/logo.svg' }"
        >
          <!-- 使用McMarkdownCard显示AI回复 -->
          <McMarkdownCard 
            :content="msg.content" 
            :typing="shouldUseTypewriter(msg, idx)"
            :typing-options="{
              step: 2,
              interval: 30,
              style: 'cursor'
            }"
            :theme="'light'"
            :md-options="{
              breaks: true,
              linkify: true,
              html: false,
              typographer: true,
              xhtmlOut: false
            }"
            @typing-end="onTypewriterComplete(idx)"
          />
          
          <!-- 底部操作按钮 -->
          <template #bottom>
            <div class="bubble-bottom-operations" v-if="msg.complete && msg.content">
              <button @click="copyMessage(msg.content)" class="operation-btn" title="复制">
                <i class="icon-copy"></i>
              </button>
              <button @click="likeMessage(msg)" class="operation-btn" title="点赞">
                <i class="icon-like" :class="{ liked: msg.liked }"></i>
              </button>
              <button @click="dislikeMessage(msg)" class="operation-btn" title="点踩">
                <i class="icon-dislike" :class="{ disliked: msg.disliked }"></i>
              </button>
              <button @click="regenerateMessage(msg)" class="operation-btn" title="重新生成">
                <i class="icon-refresh"></i>
              </button>
            </div>
            
            <!-- 显示时间戳 -->
            <div class="message-time" v-if="msg.timestamp">
              {{ formatTimestamp(msg.timestamp) }}
            </div>
          </template>
        </McBubble>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';
import { McBubble, McMarkdownCard } from '@matechat/core';
import { useChatStore } from '@/stores/chat';

const chatStore = useChatStore();

// 引用
const conversationRef = ref<HTMLElement>();

// 计算属性
const messages = computed(() => chatStore.messages);

// 响应式数据
const typewriterStates = ref<Set<number>>(new Set());

// 方法
const shouldUseTypewriter = (message: any, index: number) => {
  // 只对最新的AI消息使用打字机效果
  return message.role === 'assistant' &&
         index === messages.value.length - 1 &&
         !typewriterStates.value.has(index) &&
         chatStore.useStreaming;
};

const onTypewriterComplete = (index: number) => {
  typewriterStates.value.add(index);
};

const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content);
    // 这里可以添加复制成功的提示
    console.log('消息已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
  }
};

const likeMessage = (message: any) => {
  message.liked = !message.liked;
  if (message.liked) {
    message.disliked = false;
  }
  // 这里可以发送反馈到后端
  console.log('点赞消息:', message.liked);
};

const dislikeMessage = (message: any) => {
  message.disliked = !message.disliked;
  if (message.disliked) {
    message.liked = false;
  }
  // 这里可以发送反馈到后端
  console.log('点踩消息:', message.disliked);
};

const regenerateMessage = (message: any) => {
  // 重新生成消息
  const userMessage = messages.value[messages.value.indexOf(message) - 1];
  if (userMessage && userMessage.role === 'user') {
    chatStore.sendMessage(userMessage.content);
  }
};

const formatTimestamp = (timestamp: string): string => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

const scrollToBottom = async () => {
  await nextTick();
  if (conversationRef.value) {
    conversationRef.value.scrollTo({
      top: conversationRef.value.scrollHeight,
      behavior: 'smooth',
    });
  }
};

// 监听消息变化，自动滚动到底部
watch(
  () => messages.value.length,
  () => {
    scrollToBottom();
  },
  { flush: 'post' }
);

// 监听消息内容变化（流式响应时）
watch(
  () => messages.value.map(m => m.content).join(''),
  () => {
    scrollToBottom();
  },
  { flush: 'post' }
);
</script>

<style scoped lang="scss">
.conversation-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  width: 100%;
  padding: 20px 0;
  background: var(--devui-global-bg, #f8f9fa);
}

.chat-list {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.bubble-bottom-operations {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

:deep(.mc-bubble:hover) .bubble-bottom-operations {
  opacity: 1;
}

.operation-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--devui-aide-text, #666);
  transition: all 0.2s;
  
  &:hover {
    background: var(--devui-list-item-hover-bg, #f0f0f0);
    color: var(--devui-text, #333);
  }
  
  i {
    font-size: 14px;
  }
  
  .icon-like.liked {
    color: var(--devui-success, #67c23a);
  }
  
  .icon-dislike.disliked {
    color: var(--devui-danger, #f56c6c);
  }
}

.message-time {
  font-size: 12px;
  color: var(--devui-aide-text, #999);
  margin-top: 4px;
  text-align: right;
}

/* 自定义滚动条 */
.conversation-area {
  scrollbar-width: thin;
  scrollbar-color: var(--devui-dividing-line, #e5e5e5) transparent;
}

.conversation-area::-webkit-scrollbar {
  width: 6px;
}

.conversation-area::-webkit-scrollbar-track {
  background: transparent;
}

.conversation-area::-webkit-scrollbar-thumb {
  background: var(--devui-dividing-line, #e5e5e5);
  border-radius: 3px;
}

.conversation-area::-webkit-scrollbar-thumb:hover {
  background: var(--devui-form-control-line, #ccc);
}

@media screen and (max-width: 768px) {
  .chat-list {
    padding: 0 16px;
  }
  
  .operation-btn {
    width: 32px;
    height: 32px;
  }
}
</style>
