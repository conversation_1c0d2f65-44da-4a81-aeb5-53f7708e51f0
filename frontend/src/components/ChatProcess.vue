<template>
  <div ref="conversationRef" class="conversation-area">
    <div class="chat-list">
      <template v-for="(msg, idx) in messages" :key="idx">
        <!-- 用户消息 -->
        <McBubble
          v-if="msg.role === 'user'"
          :content="msg.content"
          :align="'right'"
          :avatarConfig="{ imgSrc: 'https://matechat.gitcode.com/png/demo/userAvatar.svg' }"
        />
        
        <!-- AI消息 -->
        <McBubble
          v-else
          :loading="msg.loading ?? false"
          :avatarConfig="{ imgSrc: 'https://matechat.gitcode.com/logo.svg' }"
        >
          <!-- AI状态显示 -->
          <div v-if="msg.status && msg.status !== 'complete'" class="ai-status">
            <div v-if="msg.status === 'thinking'" class="status-item thinking">
              <div class="status-icon">
                <i class="icon-loading spinning"></i>
              </div>
              <span class="status-text">正在思考中...</span>
            </div>

            <div v-if="msg.status === 'calling_tools' && msg.toolCalls" class="status-item tools">
              <div class="status-icon">
                <i class="icon-tool"></i>
              </div>
              <div class="tool-calls">
                <div class="status-text">正在调用工具:</div>
                <div v-for="(tool, toolIdx) in msg.toolCalls" :key="toolIdx" class="tool-call">
                  <span class="tool-name">{{ tool.name }}</span>
                  <span class="tool-status" :class="tool.status">
                    <i v-if="tool.status === 'calling'" class="icon-loading spinning"></i>
                    <i v-else-if="tool.status === 'success'" class="icon-check"></i>
                    <i v-else-if="tool.status === 'error'" class="icon-error"></i>
                  </span>
                </div>
              </div>
            </div>

            <div v-if="msg.status === 'responding'" class="status-item responding">
              <div class="status-icon">
                <i class="icon-edit"></i>
              </div>
              <span class="status-text">正在生成回复...</span>
            </div>
          </div>

          <!-- 使用McMarkdownCard显示AI回复 -->
          <McMarkdownCard
            v-if="msg.content && msg.status !== 'thinking'"
            :content="msg.content"
            :typing="shouldUseTypewriter(msg, idx)"
            :typing-options="{
              step: 2,
              interval: 30,
              style: 'cursor'
            }"
            :theme="'light'"
            :md-options="{
              breaks: true,
              linkify: true,
              html: false,
              typographer: true,
              xhtmlOut: false
            }"
            @typing-end="onTypewriterComplete(idx)"
          />
          
          <!-- 底部操作按钮 -->
          <template #bottom>
            <div class="bubble-bottom-operations" v-if="msg.complete && msg.content">
              <button @click="copyMessage(msg.content)" class="operation-btn" title="复制">
                <i class="icon-copy"></i>
              </button>
              <button @click="likeMessage(msg)" class="operation-btn" title="点赞">
                <i class="icon-like" :class="{ liked: msg.liked }"></i>
              </button>
              <button @click="dislikeMessage(msg)" class="operation-btn" title="点踩">
                <i class="icon-dislike" :class="{ disliked: msg.disliked }"></i>
              </button>
              <button @click="regenerateMessage(msg)" class="operation-btn" title="重新生成">
                <i class="icon-refresh"></i>
              </button>
            </div>
            
            <!-- 显示时间戳 -->
            <div class="message-time" v-if="msg.timestamp">
              {{ formatTimestamp(msg.timestamp) }}
            </div>
          </template>
        </McBubble>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue';
import { McBubble, McMarkdownCard } from '@matechat/core';
import { useChatStore } from '@/stores/chat';

const chatStore = useChatStore();

// 引用
const conversationRef = ref<HTMLElement>();

// 计算属性
const messages = computed(() => chatStore.messages);

// 响应式数据
const typewriterStates = ref<Set<number>>(new Set());

// 方法
const shouldUseTypewriter = (message: any, index: number) => {
  // 只对最新的AI消息使用打字机效果
  return message.role === 'assistant' &&
         index === messages.value.length - 1 &&
         !typewriterStates.value.has(index) &&
         chatStore.useStreaming;
};

const onTypewriterComplete = (index: number) => {
  typewriterStates.value.add(index);
};

const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content);
    // 这里可以添加复制成功的提示
    console.log('消息已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
  }
};

const likeMessage = (message: any) => {
  message.liked = !message.liked;
  if (message.liked) {
    message.disliked = false;
  }
  // 这里可以发送反馈到后端
  console.log('点赞消息:', message.liked);
};

const dislikeMessage = (message: any) => {
  message.disliked = !message.disliked;
  if (message.disliked) {
    message.liked = false;
  }
  // 这里可以发送反馈到后端
  console.log('点踩消息:', message.disliked);
};

const regenerateMessage = (message: any) => {
  // 重新生成消息
  const userMessage = messages.value[messages.value.indexOf(message) - 1];
  if (userMessage && userMessage.role === 'user') {
    chatStore.sendMessage(userMessage.content);
  }
};

const formatTimestamp = (timestamp: string): string => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

const scrollToBottom = async () => {
  await nextTick();
  if (conversationRef.value) {
    conversationRef.value.scrollTo({
      top: conversationRef.value.scrollHeight,
      behavior: 'smooth',
    });
  }
};

// 监听消息变化，自动滚动到底部
watch(
  () => messages.value.length,
  () => {
    scrollToBottom();
  },
  { flush: 'post' }
);

// 监听消息内容变化（流式响应时）
watch(
  () => messages.value.map(m => m.content).join(''),
  () => {
    scrollToBottom();
  },
  { flush: 'post' }
);
</script>

<style scoped lang="scss">
.conversation-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  width: 100%;
  padding: 20px 0;
  background: var(--devui-global-bg, #f8f9fa);
}

.chat-list {
  width: 100%;
  max-width: 800px; // 减小最大宽度，参考MateChat官方
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// 响应式调整
@media (max-width: 1024px) {
  .chat-list {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  .chat-list {
    padding: 0 12px;
    gap: 12px;
  }
}

.bubble-bottom-operations {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

:deep(.mc-bubble:hover) .bubble-bottom-operations {
  opacity: 1;
}

// AI状态显示样式
.ai-status {
  margin-bottom: 12px;

  .status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--devui-list-item-hover-bg, #f5f5f5);
    border-radius: 6px;
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--devui-text, #333);

    &.thinking {
      background: linear-gradient(90deg, #e3f2fd, #f3e5f5);
      border-left: 3px solid var(--devui-info, #5e7ce0);
    }

    &.tools {
      background: linear-gradient(90deg, #fff3e0, #fce4ec);
      border-left: 3px solid var(--devui-warning, #ffa726);
      flex-direction: column;
      align-items: flex-start;
    }

    &.responding {
      background: linear-gradient(90deg, #e8f5e8, #f1f8e9);
      border-left: 3px solid var(--devui-success, #67c23a);
    }
  }

  .status-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;

    i {
      font-size: 14px;
    }
  }

  .status-text {
    font-weight: 500;
  }

  .tool-calls {
    width: 100%;

    .tool-call {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 8px;
      margin: 4px 0;
      background: rgba(255, 255, 255, 0.7);
      border-radius: 4px;
      font-size: 13px;

      .tool-name {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        color: var(--devui-brand, #5e7ce0);
        font-weight: 500;
      }

      .tool-status {
        display: flex;
        align-items: center;

        &.calling {
          color: var(--devui-warning, #ffa726);
        }

        &.success {
          color: var(--devui-success, #67c23a);
        }

        &.error {
          color: var(--devui-danger, #f56c6c);
        }

        i {
          font-size: 12px;
        }
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinning {
  animation: spin 1s linear infinite;
}

.operation-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--devui-aide-text, #666);
  transition: all 0.2s;
  
  &:hover {
    background: var(--devui-list-item-hover-bg, #f0f0f0);
    color: var(--devui-text, #333);
  }
  
  i {
    font-size: 14px;
  }
  
  .icon-like.liked {
    color: var(--devui-success, #67c23a);
  }
  
  .icon-dislike.disliked {
    color: var(--devui-danger, #f56c6c);
  }
}

.message-time {
  font-size: 12px;
  color: var(--devui-aide-text, #999);
  margin-top: 4px;
  text-align: right;
}

/* 自定义滚动条 */
.conversation-area {
  scrollbar-width: thin;
  scrollbar-color: var(--devui-dividing-line, #e5e5e5) transparent;
}

.conversation-area::-webkit-scrollbar {
  width: 6px;
}

.conversation-area::-webkit-scrollbar-track {
  background: transparent;
}

.conversation-area::-webkit-scrollbar-thumb {
  background: var(--devui-dividing-line, #e5e5e5);
  border-radius: 3px;
}

.conversation-area::-webkit-scrollbar-thumb:hover {
  background: var(--devui-form-control-line, #ccc);
}

@media screen and (max-width: 768px) {
  .chat-list {
    padding: 0 16px;
  }
  
  .operation-btn {
    width: 32px;
    height: 32px;
  }
}
</style>
