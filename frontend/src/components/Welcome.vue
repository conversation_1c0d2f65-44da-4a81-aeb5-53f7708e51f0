<template>
  <div class="welcome-page">
    <!-- 介绍区域 -->
    <McIntroduction
      :logoImg="'https://matechat.gitcode.com/logo2x.svg'"
      :title="'AI智能助手'"
      :subTitle="'Hi，欢迎使用智能助手'"
      :description="description"
      class="welcome-introduction"
    />
    
    <!-- 快捷提示区域 -->
    <div class="guess-question">
      <div class="guess-title">
        <div>猜你想问</div>
        <div class="refresh-btn" @click="refreshPrompts">
          <i class="icon-refresh"></i>
          <span>换一批</span>
        </div>
      </div>
      <div class="guess-content">
        <McPrompt
          :list="currentPrompts"
          :direction="'horizontal'"
          :variant="'filled'"
          @itemClick="onPromptClick"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { McIntroduction, McPrompt } from '@matechat/core';
import { useChatStore } from '@/stores/chat';

const chatStore = useChatStore();

// 欢迎描述
const description = ref([
  '我是您的AI智能助手，可以帮助您：',
  '• 回答各种问题和提供信息',
  '• 协助分析和解决问题', 
  '• 生成内容和创意建议',
  '• 进行逻辑推理和计算'
]);

// 所有可用的提示词
const allPrompts = ref([
  {
    value: 'introduce',
    label: '你好，请介绍一下自己',
    iconConfig: { name: 'icon-user', color: '#5e7ce0' },
    desc: '了解AI助手的基本功能和能力'
  },
  {
    value: 'help',
    label: '你可以帮我做些什么？',
    iconConfig: { name: 'icon-help', color: '#67c23a' },
    desc: '了解AI助手能够提供的各种帮助'
  },
  {
    value: 'quicksort',
    label: '帮我写一个快速排序算法',
    iconConfig: { name: 'icon-code', color: '#e6a23c' },
    desc: '使用JavaScript实现快速排序算法'
  },
  {
    value: 'analysis',
    label: '帮我分析一个复杂问题',
    iconConfig: { name: 'icon-analysis', color: '#f56c6c' },
    desc: '提供问题分析和解决方案建议'
  },
  {
    value: 'creative',
    label: '给我一些创意建议',
    iconConfig: { name: 'icon-lightbulb', color: '#909399' },
    desc: '提供创新思路和灵感启发'
  },
  {
    value: 'explain',
    label: '解释一个技术概念',
    iconConfig: { name: 'icon-book', color: '#409eff' },
    desc: '深入浅出地解释复杂的技术概念'
  }
]);

// 当前显示的提示词（随机选择4个）
const currentPrompts = ref(getRandomPrompts());

// 方法
function getRandomPrompts() {
  const shuffled = [...allPrompts.value].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, 4);
}

const refreshPrompts = () => {
  currentPrompts.value = getRandomPrompts();
};

const onPromptClick = (item: any) => {
  chatStore.sendMessage(item.label);
};
</script>

<style scoped lang="scss">
.welcome-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: auto;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  gap: 32px;
}

.welcome-introduction {
  text-align: center;
  
  :deep(.mc-introduction-description) {
    font-size: var(--devui-font-size, 14px);
    color: var(--devui-aide-text, #666);
    line-height: 1.6;
  }
}

.guess-question {
  width: 100%;
  max-width: 800px;
  padding: 24px;
  border-radius: 16px;
  background: var(--devui-base-bg, white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.guess-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  & > div:first-child {
    font-weight: 600;
    font-size: 18px;
    line-height: 24px;
    color: var(--devui-text, #333);
  }
  
  .refresh-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    color: var(--devui-aide-text, #666);
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.2s;
    
    &:hover {
      background: var(--devui-list-item-hover-bg, #f0f0f0);
      color: var(--devui-brand, #5e7ce0);
    }
    
    i {
      font-size: 14px;
    }
  }
}

.guess-content {
  :deep(.mc-prompt-container) {
    gap: 12px;
  }
  
  :deep(.mc-prompt-item) {
    flex: 1;
    min-width: 0;
    
    .mc-prompt-content {
      padding: 16px;
      border-radius: 12px;
      background: var(--devui-area, #f8f9fa);
      border: 1px solid var(--devui-dividing-line, #e5e5e5);
      transition: all 0.2s;
      
      &:hover {
        background: var(--devui-list-item-hover-bg, #e3f2fd);
        border-color: var(--devui-brand, #5e7ce0);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(94, 124, 224, 0.15);
      }
    }
    
    .mc-prompt-label {
      font-size: 14px;
      font-weight: 500;
      color: var(--devui-text, #333);
      line-height: 1.4;
    }
    
    .mc-prompt-desc {
      font-size: 12px;
      color: var(--devui-aide-text, #666);
      margin-top: 4px;
      line-height: 1.3;
    }
  }
}

@media screen and (max-width: 768px) {
  .welcome-page {
    padding: 20px 16px;
    gap: 24px;
  }
  
  .guess-question {
    padding: 20px;
  }
  
  .guess-title > div:first-child {
    font-size: 16px;
  }
  
  .refresh-btn span {
    display: none;
  }
  
  :deep(.mc-prompt-container) {
    flex-direction: column;
  }
}
</style>
