<template>
  <div class="history-list-container">
    <!-- 头部 -->
    <div class="history-header">
      <div class="history-header-left">
        <span class="history-title">聊天历史</span>
      </div>
      <div class="history-header-right">
        <button @click="createNewSession" class="new-session-btn" title="新建会话">
          <i class="icon-add"></i>
        </button>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="history-search">
      <input 
        v-model="searchKey" 
        placeholder="搜索会话..." 
        class="search-input"
        @input="onSearch"
      />
    </div>

    <!-- 会话列表 -->
    <div class="history-list-box">
      <McList 
        :data="sessionListData"
        @select="onSessionSelect"
        class="session-list"
        style="height: calc(100vh - 140px);"
      >
        <template #item="{ item }">
          <div class="session-item-content" :class="{ active: item.active }">
            <div class="session-info">
              <div class="session-title">{{ item.label }}</div>
              <div class="session-meta">
                <span class="message-count">{{ item.messageCount }} 条消息</span>
                <span class="last-time">{{ formatTime(item.lastActiveTime) }}</span>
              </div>
            </div>
            <div class="session-actions">
              <button @click.stop="startEdit(item.value, item.label)" class="action-btn edit-btn" title="重命名">
                <i class="icon-edit"></i>
              </button>
              <button @click.stop="deleteSession(item.value)" class="action-btn delete-btn" title="删除">
                <i class="icon-delete"></i>
              </button>
            </div>
          </div>
        </template>
      </McList>
    </div>

    <!-- 编辑对话框 -->
    <div v-if="editingSessionId" class="edit-modal" @click="cancelEdit">
      <div class="edit-dialog" @click.stop>
        <h4>重命名会话</h4>
        <input 
          v-model="editingTitle"
          @keyup.enter="saveSessionTitle"
          @keyup.escape="cancelEdit"
          class="edit-input"
          ref="editInput"
        />
        <div class="edit-actions">
          <button @click="saveSessionTitle" class="save-btn">保存</button>
          <button @click="cancelEdit" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { McList } from '@matechat/core';
import { useChatStore } from '@/stores/chat';

const chatStore = useChatStore();

// 响应式数据
const searchKey = ref('');
const editingSessionId = ref<string | null>(null);
const editingTitle = ref('');
const editInput = ref<HTMLInputElement>();

// 计算属性
const filteredSessions = computed(() => {
  if (!searchKey.value) return chatStore.sessions;
  return chatStore.sessions.filter(session => 
    session.title.toLowerCase().includes(searchKey.value.toLowerCase())
  );
});

// 转换为McList需要的数据格式
const sessionListData = computed(() => {
  return filteredSessions.value.map(session => ({
    label: session.title || '新对话',
    value: session.id,
    messageCount: session.messageCount || 0,
    lastActiveTime: session.lastActiveTime || new Date(),
    active: session.id === chatStore.currentSessionId
  }));
});

// 方法
const createNewSession = () => {
  chatStore.createSession();
};

const onSessionSelect = (item: any) => {
  chatStore.selectSession(item.value);
};

const deleteSession = (sessionId: string) => {
  if (confirm('确定要删除这个会话吗？删除后无法恢复。')) {
    chatStore.deleteSession(sessionId);
  }
};

const startEdit = async (sessionId: string, currentTitle: string) => {
  editingSessionId.value = sessionId;
  editingTitle.value = currentTitle || '新对话';
  
  await nextTick();
  if (editInput.value) {
    editInput.value.focus();
    editInput.value.select();
  }
};

const saveSessionTitle = () => {
  if (editingSessionId.value && editingTitle.value.trim()) {
    chatStore.renameSession(editingSessionId.value, editingTitle.value.trim());
  }
  cancelEdit();
};

const cancelEdit = () => {
  editingSessionId.value = null;
  editingTitle.value = '';
};

const onSearch = () => {
  // 搜索逻辑已在computed中处理
};

const formatTime = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  return date.toLocaleDateString('zh-CN');
};
</script>

<style scoped lang="scss">
.history-list-container {
  width: 300px;
  background: var(--devui-base-bg, white);
  border-right: 1px solid var(--devui-dividing-line, #e5e5e5);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.history-header {
  padding: 16px;
  border-bottom: 1px solid var(--devui-dividing-line, #e5e5e5);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--devui-text, #333);
}

.new-session-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  color: var(--devui-text, #333);
  
  &:hover {
    background: var(--devui-list-item-hover-bg, #f0f0f0);
  }
}

.history-search {
  padding: 16px;
  border-bottom: 1px solid var(--devui-dividing-line, #e5e5e5);
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--devui-form-control-line, #ddd);
  border-radius: 6px;
  font-size: 14px;
  background: var(--devui-base-bg, white);
  color: var(--devui-text, #333);
  
  &:focus {
    outline: none;
    border-color: var(--devui-brand, #5e7ce0);
  }
}

.history-list-box {
  flex: 1;
  overflow: hidden;
}

.session-list {
  height: 100%;
}

.session-item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 6px;
  margin: 4px 8px;
  
  &:hover {
    background: var(--devui-list-item-hover-bg, #f8f9fa);
  }
  
  &.active {
    background: var(--devui-list-item-active-bg, #e3f2fd);
    border-left: 3px solid var(--devui-brand, #2196f3);
  }
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-weight: 500;
  color: var(--devui-text, #333);
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-meta {
  font-size: 12px;
  color: var(--devui-aide-text, #666);
  display: flex;
  gap: 8px;
}

.session-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.session-item-content:hover .session-actions {
  opacity: 1;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--devui-aide-text, #666);
  
  &:hover {
    background: var(--devui-list-item-hover-bg, #e0e0e0);
  }
}

.edit-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.edit-dialog {
  background: var(--devui-base-bg, white);
  padding: 24px;
  border-radius: 8px;
  min-width: 300px;
}

.edit-dialog h4 {
  margin: 0 0 16px 0;
  color: var(--devui-text, #333);
}

.edit-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--devui-form-control-line, #ddd);
  border-radius: 4px;
  margin-bottom: 16px;
  background: var(--devui-base-bg, white);
  color: var(--devui-text, #333);
}

.edit-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.save-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.save-btn {
  background: var(--devui-brand, #2196f3);
  color: white;
}

.cancel-btn {
  background: var(--devui-list-item-hover-bg, #f0f0f0);
  color: var(--devui-text, #333);
}
</style>
