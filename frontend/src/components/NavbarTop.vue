<template>
  <div class="navbar-top-container">
    <McHeader 
      :title="'AI智能助手'" 
      :logoImg="'https://matechat.gitcode.com/logo.svg'"
    >
      <template #operationArea>
        <div class="header-operations">
          <!-- 连接状态 -->
          <div class="connection-status" :class="{ connected: isConnected }">
            <span class="status-dot"></span>
            {{ isConnected ? '已连接' : '未连接' }}
          </div>
          
          <!-- 流式响应开关 -->
          <div class="streaming-toggle">
            <label class="toggle-label">
              <input 
                type="checkbox" 
                v-model="useStreaming" 
                class="toggle-input"
              />
              <span class="toggle-slider"></span>
              <span class="toggle-text">流式响应</span>
            </label>
          </div>
          
          <!-- 清空对话按钮 -->
          <button @click="clearChat" class="clear-btn">
            <i class="icon-delete"></i>
            <span>清空对话</span>
          </button>
          
          <!-- 新建会话按钮 -->
          <button @click="newChat" class="new-chat-btn">
            <i class="icon-add"></i>
            <span>新建会话</span>
          </button>
        </div>
      </template>
    </McHeader>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { McHeader } from '@matechat/core';
import { useChatStore } from '@/stores/chat';

const chatStore = useChatStore();

// 计算属性
const isConnected = computed(() => chatStore.isConnected);
const useStreaming = computed({
  get: () => chatStore.useStreaming,
  set: (value) => chatStore.setStreaming(value)
});

// 方法
const clearChat = () => {
  if (confirm('确定要清空当前对话吗？')) {
    chatStore.clearCurrentSession();
  }
};

const newChat = () => {
  chatStore.createSession();
};
</script>

<style scoped lang="scss">
.navbar-top-container {
  border-bottom: 1px solid var(--devui-dividing-line, #e5e5e5);
  background: var(--devui-base-bg, white);
}

.header-operations {
  display: flex;
  align-items: center;
  gap: 16px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--devui-aide-text, #666);
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--devui-danger, #f56c6c);
  }
  
  &.connected .status-dot {
    background: var(--devui-success, #67c23a);
  }
}

.streaming-toggle {
  .toggle-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    color: var(--devui-text, #333);
  }
  
  .toggle-input {
    position: relative;
    width: 40px;
    height: 20px;
    appearance: none;
    background: var(--devui-form-control-line, #ddd);
    border-radius: 10px;
    cursor: pointer;
    transition: background 0.3s;
    
    &:checked {
      background: var(--devui-brand, #5e7ce0);
    }
    
    &::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 16px;
      height: 16px;
      background: white;
      border-radius: 50%;
      transition: transform 0.3s;
    }
    
    &:checked::before {
      transform: translateX(20px);
    }
  }
  
  .toggle-text {
    font-size: 14px;
    color: var(--devui-text, #333);
  }
}

.clear-btn, .new-chat-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: none;
  border: 1px solid var(--devui-form-control-line, #ddd);
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: var(--devui-text, #333);
  transition: all 0.2s;
  
  &:hover {
    background: var(--devui-list-item-hover-bg, #f0f0f0);
    border-color: var(--devui-brand, #5e7ce0);
  }
  
  i {
    font-size: 14px;
  }
}

.new-chat-btn {
  background: var(--devui-brand, #5e7ce0);
  color: white;
  border-color: var(--devui-brand, #5e7ce0);
  
  &:hover {
    background: var(--devui-brand-hover, #4a6bc7);
    border-color: var(--devui-brand-hover, #4a6bc7);
  }
}

@media screen and (max-width: 768px) {
  .header-operations {
    gap: 8px;
  }
  
  .clear-btn span,
  .new-chat-btn span,
  .toggle-text {
    display: none;
  }
}
</style>
