<template>
  <div class="prompt-edit-overlay" @click="handleOverlayClick">
    <div class="prompt-edit-dialog" @click.stop>
      <div class="dialog-header">
        <h3 class="dialog-title">
          {{ isEditing ? '编辑提示词' : '创建提示词' }}
        </h3>
        <button @click="$emit('cancel')" class="close-btn">✕</button>
      </div>

      <div class="dialog-content">
        <form @submit.prevent="handleSave" class="prompt-form">
          <!-- 基本信息 -->
          <div class="form-section">
            <h4 class="section-title">基本信息</h4>
            
            <div class="form-field">
              <label class="field-label">标题 <span class="required">*</span></label>
              <input
                v-model="formData.title"
                placeholder="请输入提示词标题"
                class="field-input"
                required
              />
            </div>

            <div class="form-field">
              <label class="field-label">描述</label>
              <input
                v-model="formData.description"
                placeholder="简要描述提示词的用途"
                class="field-input"
              />
            </div>

            <div class="form-row">
              <div class="form-field">
                <label class="field-label">分类 <span class="required">*</span></label>
                <select v-model="formData.category" class="field-select" required>
                  <option value="">请选择分类</option>
                  <option
                    v-for="category in categories"
                    :key="category.id"
                    :value="category.id"
                  >
                    {{ category.icon }} {{ category.name }}
                  </option>
                </select>
              </div>

              <div class="form-field">
                <label class="field-label">图标</label>
                <input
                  v-model="formData.icon"
                  placeholder="📝"
                  class="field-input icon-input"
                  maxlength="2"
                />
              </div>
            </div>

            <div class="form-field">
              <label class="field-label">标签</label>
              <div class="tags-input">
                <div class="tags-list">
                  <span
                    v-for="(tag, index) in formData.tags"
                    :key="index"
                    class="tag-item"
                  >
                    {{ tag }}
                    <button
                      type="button"
                      @click="removeTag(index)"
                      class="tag-remove"
                    >
                      ✕
                    </button>
                  </span>
                </div>
                <input
                  v-model="newTag"
                  @keyup.enter="addTag"
                  placeholder="输入标签后按回车"
                  class="tag-input"
                />
              </div>
            </div>
          </div>

          <!-- 内容 -->
          <div class="form-section">
            <h4 class="section-title">提示词内容</h4>
            
            <div class="form-field">
              <label class="field-label">内容 <span class="required">*</span></label>
              <textarea
                v-model="formData.content"
                placeholder="请输入提示词内容，使用 {变量名} 来定义变量"
                class="field-textarea"
                rows="6"
                required
              ></textarea>
              <div class="field-hint">
                提示：使用 {变量名} 来定义可配置的变量，如 {topic}、{style} 等
              </div>
            </div>
          </div>

          <!-- 变量配置 -->
          <div class="form-section" v-if="detectedVariables.length > 0">
            <h4 class="section-title">变量配置</h4>
            
            <div
              v-for="(variable, index) in formData.variables"
              :key="index"
              class="variable-config"
            >
              <div class="variable-header">
                <span class="variable-name">{{ variable.name }}</span>
                <button
                  type="button"
                  @click="removeVariable(index)"
                  class="variable-remove"
                >
                  删除
                </button>
              </div>

              <div class="variable-fields">
                <div class="form-field">
                  <label class="field-label">显示名称</label>
                  <input
                    v-model="variable.label"
                    placeholder="变量的显示名称"
                    class="field-input"
                  />
                </div>

                <div class="form-row">
                  <div class="form-field">
                    <label class="field-label">类型</label>
                    <select v-model="variable.type" class="field-select">
                      <option value="text">文本</option>
                      <option value="textarea">多行文本</option>
                      <option value="select">选择框</option>
                      <option value="number">数字</option>
                    </select>
                  </div>

                  <div class="form-field">
                    <label class="field-label">
                      <input
                        type="checkbox"
                        v-model="variable.required"
                        class="field-checkbox"
                      />
                      必填
                    </label>
                  </div>
                </div>

                <div class="form-field">
                  <label class="field-label">占位符</label>
                  <input
                    v-model="variable.placeholder"
                    placeholder="输入提示文本"
                    class="field-input"
                  />
                </div>

                <div class="form-field" v-if="variable.type === 'select'">
                  <label class="field-label">选项</label>
                  <textarea
                    v-model="variable.optionsText"
                    placeholder="每行一个选项"
                    class="field-textarea"
                    rows="3"
                    @input="updateVariableOptions(variable)"
                  ></textarea>
                </div>

                <div class="form-field">
                  <label class="field-label">默认值</label>
                  <input
                    v-model="variable.defaultValue"
                    placeholder="默认值"
                    class="field-input"
                  />
                </div>
              </div>
            </div>

            <div v-if="unConfiguredVariables.length > 0" class="unconfigured-variables">
              <p>检测到未配置的变量：</p>
              <div class="variable-suggestions">
                <button
                  v-for="varName in unConfiguredVariables"
                  :key="varName"
                  type="button"
                  @click="addVariableConfig(varName)"
                  class="variable-suggestion"
                >
                  + {{ varName }}
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="dialog-footer">
        <button type="button" @click="$emit('cancel')" class="cancel-btn">
          取消
        </button>
        <button type="button" @click="handleSave" class="save-btn" :disabled="!isValid">
          {{ isEditing ? '保存' : '创建' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { promptCategories, PromptManager, type PromptItem, type PromptVariable } from '@/data/prompts';

interface Props {
  prompt: PromptItem | null;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  save: [prompt: PromptItem];
  cancel: [];
}>();

// 响应式数据
const formData = ref({
  title: '',
  description: '',
  content: '',
  category: '',
  icon: '📝',
  tags: [] as string[],
  variables: [] as (PromptVariable & { optionsText?: string })[]
});

const newTag = ref('');

// 计算属性
const isEditing = computed(() => !!props.prompt);

const categories = computed(() => promptCategories.filter(c => c.id !== 'custom'));

const isValid = computed(() => {
  return formData.value.title.trim() && 
         formData.value.content.trim() && 
         formData.value.category;
});

const detectedVariables = computed(() => {
  const matches = formData.value.content.match(/\{([^}]+)\}/g);
  if (!matches) return [];
  
  return [...new Set(matches.map(match => match.slice(1, -1)))];
});

const unConfiguredVariables = computed(() => {
  const configuredNames = formData.value.variables.map(v => v.name);
  return detectedVariables.value.filter(name => !configuredNames.includes(name));
});

// 方法
const handleOverlayClick = () => {
  emit('cancel');
};

const addTag = () => {
  const tag = newTag.value.trim();
  if (tag && !formData.value.tags.includes(tag)) {
    formData.value.tags.push(tag);
    newTag.value = '';
  }
};

const removeTag = (index: number) => {
  formData.value.tags.splice(index, 1);
};

const addVariableConfig = (varName: string) => {
  formData.value.variables.push({
    name: varName,
    label: varName,
    type: 'text',
    required: false,
    placeholder: '',
    defaultValue: ''
  });
};

const removeVariable = (index: number) => {
  formData.value.variables.splice(index, 1);
};

const updateVariableOptions = (variable: PromptVariable & { optionsText?: string }) => {
  if (variable.optionsText) {
    variable.options = variable.optionsText.split('\n').filter(opt => opt.trim());
  }
};

const handleSave = () => {
  if (!isValid.value) return;

  // 清理变量配置
  const cleanVariables = formData.value.variables.map(v => {
    const { optionsText, ...cleanVar } = v;
    return cleanVar;
  });

  const promptData = {
    ...formData.value,
    variables: cleanVariables,
    category: formData.value.category || 'custom'
  };

  if (isEditing.value && props.prompt) {
    // 更新现有提示词
    PromptManager.updatePrompt(props.prompt.id, promptData);
    emit('save', { ...props.prompt, ...promptData });
  } else {
    // 创建新提示词
    const newPrompt = PromptManager.saveCustomPrompt(promptData);
    emit('save', newPrompt);
  }
};

const initializeForm = () => {
  if (props.prompt) {
    formData.value = {
      title: props.prompt.title,
      description: props.prompt.description || '',
      content: props.prompt.content,
      category: props.prompt.category,
      icon: props.prompt.icon || '📝',
      tags: [...(props.prompt.tags || [])],
      variables: (props.prompt.variables || []).map(v => ({
        ...v,
        optionsText: v.options ? v.options.join('\n') : ''
      }))
    };
  } else {
    formData.value = {
      title: '',
      description: '',
      content: '',
      category: '',
      icon: '📝',
      tags: [],
      variables: []
    };
  }
};

// 监听变化
watch(() => props.prompt, () => {
  initializeForm();
}, { immediate: true });

// 生命周期
onMounted(() => {
  initializeForm();
});
</script>

<style scoped>
.prompt-edit-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.prompt-edit-dialog {
  background: white;
  border-radius: 8px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s;
}

.close-btn:hover {
  background: #f0f0f0;
}

.dialog-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.form-field {
  margin-bottom: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.field-label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  font-size: 14px;
}

.required {
  color: #ff4d4f;
}

.field-input,
.field-textarea,
.field-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.field-input:focus,
.field-textarea:focus,
.field-select:focus {
  border-color: #1890ff;
}

.field-textarea {
  resize: vertical;
  font-family: inherit;
}

.icon-input {
  width: 60px;
  text-align: center;
}

.field-hint {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.tags-input {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  min-height: 40px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: #f0f0f0;
  border-radius: 12px;
  font-size: 12px;
}

.tag-remove {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 10px;
  color: #999;
}

.tag-input {
  border: none;
  outline: none;
  flex: 1;
  min-width: 120px;
  font-size: 14px;
}

.variable-config {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.variable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.variable-name {
  font-weight: 600;
  color: #1890ff;
}

.variable-remove {
  background: none;
  border: none;
  color: #ff4d4f;
  cursor: pointer;
  font-size: 12px;
}

.field-checkbox {
  margin-right: 6px;
}

.unconfigured-variables {
  padding: 12px;
  background: #fff7e6;
  border: 1px solid #ffd666;
  border-radius: 6px;
  margin-top: 16px;
}

.variable-suggestions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.variable-suggestion {
  padding: 4px 8px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s;
}

.variable-suggestion:hover {
  background: #40a9ff;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
}

.cancel-btn,
.save-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.cancel-btn {
  background: white;
  color: #666;
}

.cancel-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.save-btn {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.save-btn:hover:not(:disabled) {
  background: #40a9ff;
}

.save-btn:disabled {
  background: #f5f5f5;
  color: #ccc;
  border-color: #e8e8e8;
  cursor: not-allowed;
}
</style>
