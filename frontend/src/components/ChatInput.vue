<template>
  <div class="input-container">
    <!-- 快捷提示词（仅在有消息时显示） -->
    <div v-if="hasMessages" class="shortcut-prompts">
      <McPrompt
        :list="quickPrompts"
        :direction="'horizontal'"
        :variant="'transparent'"
        @itemClick="onPromptClick"
      />
    </div>

    <!-- 输入框 -->
    <McInput
      :value="inputValue"
      :maxLength="2000"
      :loading="isLoading"
      variant="borderless"
      showCount
      @change="onInputChange"
      @submit="onSubmit"
    >
      <template #extra>
        <div class="input-foot-wrapper">
          <div class="input-foot-left">
            <!-- 智能体 -->
            <div class="input-action-item" @click="onAgentClick">
              <i class="icon-user"></i>
              <span>智能体</span>
            </div>
            
            <!-- 分隔线 -->
            <span class="input-foot-dividing-line"></span>
            
            <!-- 字符计数 -->
            <span class="input-foot-maxlength">
              {{ inputValue.length }}/2000
            </span>
          </div>
          
          <div class="input-foot-right">
            <!-- 清空输入按钮 -->
            <button 
              @click="clearInput" 
              :disabled="!inputValue" 
              class="clear-input-btn"
            >
              <i class="icon-clear"></i>
              <span>清空输入</span>
            </button>
          </div>
        </div>
      </template>
    </McInput>

    <!-- 免责声明 -->
    <div class="statement-box">
      <span>AI生成内容仅供参考，不代表真实观点</span>
      <span class="separator"></span>
      <span class="link-span">隐私声明</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { McInput, McPrompt } from '@matechat/core';
import { useChatStore } from '@/stores/chat';

const chatStore = useChatStore();

// 响应式数据
const inputValue = ref('');

// 计算属性
const isLoading = computed(() => chatStore.isLoading);
const hasMessages = computed(() => chatStore.hasMessages);

// 快捷提示词
const quickPrompts = ref([
  {
    value: 'continue',
    label: '继续',
    iconConfig: { name: 'icon-arrow-right', color: '#5e7ce0' }
  },
  {
    value: 'explain',
    label: '详细解释',
    iconConfig: { name: 'icon-info', color: '#67c23a' }
  },
  {
    value: 'example',
    label: '举个例子',
    iconConfig: { name: 'icon-lightbulb', color: '#e6a23c' }
  },
  {
    value: 'optimize',
    label: '优化建议',
    iconConfig: { name: 'icon-star', color: '#f56c6c' }
  }
]);

// 方法
const onInputChange = (value: string) => {
  inputValue.value = value;
};

const onSubmit = (value: string) => {
  if (!value.trim() || isLoading.value) return;
  
  chatStore.sendMessage(value.trim());
  inputValue.value = '';
};

const onPromptClick = (item: any) => {
  const promptText = item.label;
  inputValue.value = promptText;
  onSubmit(promptText);
};

const onAgentClick = () => {
  // 这里可以实现智能体选择功能
  console.log('智能体功能待实现');
};

const clearInput = () => {
  inputValue.value = '';
};

// 监听store中的消息发送，清空输入框
chatStore.$onAction(({ name }) => {
  if (name === 'sendMessage') {
    inputValue.value = '';
  }
});
</script>

<style scoped lang="scss">
.input-container {
  width: 100%;
  max-width: min(800px, 100%); // 与聊天区域保持一致的自适应宽度
  margin: 0 auto;
  padding: 0 20px 20px 20px;
  background: var(--devui-base-bg, white);
}

// 响应式调整
@media (max-width: 1200px) {
  .input-container {
    max-width: min(700px, 100%);
    padding: 0 16px 16px 16px;
  }
}

@media (max-width: 768px) {
  .input-container {
    max-width: 100%;
    padding: 0 12px 12px 12px;
  }
}

// 超宽屏幕优化
@media (min-width: 1920px) {
  .input-container {
    max-width: min(900px, 100%);
    padding: 0 40px 20px 40px;
  }
}

.shortcut-prompts {
  margin-bottom: 12px;
  
  :deep(.mc-prompt-container) {
    gap: 8px;
    justify-content: center;
  }
  
  :deep(.mc-prompt-item) {
    .mc-prompt-content {
      padding: 8px 16px;
      border-radius: 20px;
      background: var(--devui-area, #f8f9fa);
      border: 1px solid var(--devui-dividing-line, #e5e5e5);
      transition: all 0.2s;
      
      &:hover {
        background: var(--devui-list-item-hover-bg, #e3f2fd);
        border-color: var(--devui-brand, #5e7ce0);
      }
    }
    
    .mc-prompt-label {
      font-size: 14px;
      color: var(--devui-text, #333);
    }
  }
}

.input-foot-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  margin-right: 8px;
}

.input-foot-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-action-item {
  display: flex;
  align-items: center;
  gap: 6px;
  height: 30px;
  color: var(--devui-text, #333);
  font-size: 14px;
  border-radius: 4px;
  padding: 6px 8px;
  cursor: pointer;
  transition: background 0.2s;
  
  &:hover {
    background: var(--devui-list-item-hover-bg, #f0f0f0);
  }
  
  i {
    font-size: 14px;
  }
  
  span {
    font-size: 14px;
  }
}

.input-foot-dividing-line {
  width: 1px;
  height: 14px;
  background: var(--devui-dividing-line, #e5e5e5);
}

.input-foot-maxlength {
  font-size: 14px;
  color: var(--devui-aide-text, #666);
}

.input-foot-right {
  display: flex;
  align-items: center;
}

.clear-input-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: none;
  border: 1px solid var(--devui-form-control-line, #ddd);
  border-radius: 16px;
  cursor: pointer;
  font-size: 14px;
  color: var(--devui-text, #333);
  transition: all 0.2s;
  
  &:hover:not(:disabled) {
    background: var(--devui-list-item-hover-bg, #f0f0f0);
    border-color: var(--devui-brand, #5e7ce0);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  i {
    font-size: 14px;
  }
}

.statement-box {
  font-size: 12px;
  margin-top: 12px;
  color: var(--devui-aide-text, #999);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  .separator {
    width: 1px;
    height: 12px;
    background: var(--devui-dividing-line, #e5e5e5);
  }
  
  .link-span {
    cursor: pointer;
    text-decoration: underline;
    
    &:hover {
      color: var(--devui-brand, #5e7ce0);
    }
  }
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .input-container {
    padding: 0 16px 16px 16px;
  }
  
  .shortcut-prompts {
    :deep(.mc-prompt-container) {
      flex-wrap: wrap;
    }
  }
  
  .input-action-item span,
  .clear-input-btn span {
    display: none;
  }
  
  .statement-box {
    flex-direction: column;
    gap: 4px;
    
    .separator {
      display: none;
    }
  }
}

@media screen and (max-width: 520px) {
  .input-foot-left {
    gap: 8px;
  }
  
  .input-action-item span {
    display: none;
  }
}
</style>
