import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { chatAPI, streamingService, type ChatMessage } from '@/services/api';

export interface Session {
  id: string;
  title: string;
  messageCount: number;
  lastActiveTime: Date;
  createdAt: Date;
  messages: ChatMessage[];
}

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref<ChatMessage[]>([]);
  const currentThreadId = ref<string>('');
  const isLoading = ref(false);
  const isConnected = ref(false);
  const websocket = ref<WebSocket | null>(null);
  const useStreaming = ref(true);
  const startChat = ref(false);

  // 会话管理状态
  const sessions = ref<Session[]>([]);
  const currentSessionId = ref<string>('');

  // 计算属性
  const hasMessages = computed(() => messages.value.length > 0);
  const lastMessage = computed(() => 
    messages.value[messages.value.length - 1]
  );

  // 方法
  const generateThreadId = () => {
    return `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const addMessage = (message: ChatMessage) => {
    messages.value.push({
      ...message,
      timestamp: message.timestamp || new Date().toISOString(),
    });

    // 更新当前会话信息
    updateCurrentSessionTitle();
    if (currentSessionId.value) {
      const session = sessions.value.find(s => s.id === currentSessionId.value);
      if (session) {
        session.messageCount = messages.value.length;
        session.lastActiveTime = new Date();
      }
    }
  };

  const sendMessage = async (content: string, streaming?: boolean) => {
    if (!content.trim()) return;

    // 设置开始聊天状态
    startChat.value = true;

    // 确保有线程ID
    if (!currentThreadId.value) {
      currentThreadId.value = generateThreadId();
    }

    // 添加用户消息
    addMessage({
      role: 'user',
      content: content.trim(),
    });

    isLoading.value = true;

    const shouldUseStreaming = streaming !== undefined ? streaming : useStreaming.value;

    if (shouldUseStreaming) {
      // 使用流式响应
      await sendStreamingMessage(content.trim());
    } else {
      // 使用传统方式
      await sendRegularMessage(content.trim());
    }
  };

  const sendStreamingMessage = async (content: string) => {
    // 添加AI消息占位符，显示思考状态
    const aiMessageIndex = messages.value.length;
    addMessage({
      role: 'assistant',
      content: '',
      status: 'thinking',
      thinking: true,
    });

    try {
      // 确保WebSocket连接
      if (!websocket.value || websocket.value.readyState !== WebSocket.OPEN) {
        connectWebSocket();
        // 等待连接建立
        await new Promise((resolve, reject) => {
          const checkConnection = () => {
            if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
              resolve(true);
            } else if (websocket.value && websocket.value.readyState === WebSocket.CLOSED) {
              reject(new Error('WebSocket连接失败'));
            } else {
              setTimeout(checkConnection, 100);
            }
          };
          checkConnection();
        });
      }

      // 通过WebSocket发送消息
      if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
        websocket.value.send(JSON.stringify({
          type: 'chat',
          message: content,
          thread_id: currentThreadId.value
        }));
        console.log('消息已通过WebSocket发送');
      } else {
        throw new Error('WebSocket连接不可用');
      }
    } catch (error) {
      console.error('发送流式消息失败:', error);
      if (messages.value[aiMessageIndex]) {
        messages.value[aiMessageIndex].content = '抱歉，发送消息时出现错误，请重试。';
        messages.value[aiMessageIndex].status = 'complete';
      }
      isLoading.value = false;
    }
  };

  const sendRegularMessage = async (content: string) => {
    try {
      // 发送到后端
      const response = await chatAPI.sendMessage({
        message: content,
        thread_id: currentThreadId.value,
      });

      // 添加AI回复
      addMessage({
        role: 'assistant',
        content: response.message,
      });

    } catch (error) {
      console.error('发送消息失败:', error);
      addMessage({
        role: 'assistant',
        content: '抱歉，发送消息时出现错误，请重试。',
      });
    } finally {
      isLoading.value = false;
    }
  };

  // 心跳机制相关
  const heartbeatInterval = ref<NodeJS.Timeout | null>(null);
  const reconnectTimeout = ref<NodeJS.Timeout | null>(null);
  const reconnectAttempts = ref(0);
  const maxReconnectAttempts = 5;

  const connectWebSocket = () => {
    if (!currentThreadId.value) {
      currentThreadId.value = generateThreadId();
    }

    try {
      // 清理之前的连接
      if (websocket.value) {
        websocket.value.close();
      }

      websocket.value = chatAPI.connectWebSocket(currentThreadId.value);

      websocket.value.onopen = () => {
        isConnected.value = true;
        reconnectAttempts.value = 0;
        console.log('WebSocket连接已建立');

        // 启动心跳机制
        startHeartbeat();
      };

      websocket.value.onmessage = (event) => {
        const data = JSON.parse(event.data);

        // 处理心跳响应
        if (data.type === 'pong') {
          console.log('收到心跳响应');
          return;
        }

        // 处理连接确认
        if (data.type === 'connection') {
          console.log('连接确认:', data);
          return;
        }

        // 处理AI流式响应
        if (data.type === 'AIMessage') {
          const lastMsg = messages.value[messages.value.length - 1];
          if (lastMsg && lastMsg.role === 'assistant') {
            // 更新AI消息状态和内容
            if (data.tool_calls && data.tool_calls.length > 0) {
              // 有工具调用
              lastMsg.status = 'calling_tools';
              lastMsg.toolCalls = data.tool_calls.map((tc: any) => ({
                name: tc.name,
                args: tc.args,
                status: 'calling'
              }));
              lastMsg.thinking = false;
            } else if (data.content) {
              // 开始响应
              lastMsg.status = 'responding';
              lastMsg.content = data.content;
              lastMsg.thinking = false;
            }
          }
        }

        // 处理工具调用结果
        if (data.type === 'ToolMessage') {
          const lastMsg = messages.value[messages.value.length - 1];
          if (lastMsg && lastMsg.role === 'assistant' && lastMsg.toolCalls) {
            // 更新工具调用状态
            lastMsg.toolCalls.forEach(tool => {
              tool.status = 'success';
              tool.result = data.content;
            });
            lastMsg.status = 'responding';
          }
        }

        // 处理最终响应完成
        if (data.type === 'complete') {
          const lastMsg = messages.value[messages.value.length - 1];
          if (lastMsg && lastMsg.role === 'assistant') {
            lastMsg.status = 'complete';
            lastMsg.thinking = false;
            lastMsg.loading = false;
            isLoading.value = false;
          }
        }
      };

      websocket.value.onclose = (event) => {
        isConnected.value = false;
        stopHeartbeat();
        console.log('WebSocket连接已关闭:', event.code, event.reason);

        // 非正常关闭时尝试重连
        if (event.code !== 1000 && reconnectAttempts.value < maxReconnectAttempts) {
          scheduleReconnect();
        }
      };

      websocket.value.onerror = (error) => {
        console.error('WebSocket错误:', error);
        isConnected.value = false;
        stopHeartbeat();
      };

    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }
  };

  // 心跳机制
  const startHeartbeat = () => {
    stopHeartbeat(); // 清理之前的心跳

    heartbeatInterval.value = setInterval(() => {
      if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
        websocket.value.send(JSON.stringify({ type: 'ping' }));
        console.log('发送心跳');
      }
    }, 30000); // 每30秒发送一次心跳
  };

  const stopHeartbeat = () => {
    if (heartbeatInterval.value) {
      clearInterval(heartbeatInterval.value);
      heartbeatInterval.value = null;
    }
  };

  // 重连机制
  const scheduleReconnect = () => {
    if (reconnectTimeout.value) {
      clearTimeout(reconnectTimeout.value);
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 30000); // 指数退避，最大30秒
    console.log(`${delay}ms后尝试重连 (第${reconnectAttempts.value + 1}次)`);

    reconnectTimeout.value = setTimeout(() => {
      reconnectAttempts.value++;
      connectWebSocket();
    }, delay);
  };

  const disconnectWebSocket = () => {
    // 清理心跳和重连定时器
    stopHeartbeat();
    if (reconnectTimeout.value) {
      clearTimeout(reconnectTimeout.value);
      reconnectTimeout.value = null;
    }

    if (websocket.value) {
      websocket.value.close(1000, '用户主动断开'); // 正常关闭
      websocket.value = null;
      isConnected.value = false;
    }
  };

  const clearMessages = () => {
    messages.value = [];
    currentThreadId.value = '';

    // 清空当前会话的消息
    if (currentSessionId.value) {
      const session = sessions.value.find(s => s.id === currentSessionId.value);
      if (session) {
        session.messages = [];
        session.messageCount = 0;
        session.title = '新对话';
      }
    }
  };

  const loadSessionHistory = async (threadId: string) => {
    try {
      const history = await chatAPI.getSessionHistory(threadId);
      messages.value = history;
      currentThreadId.value = threadId;
    } catch (error) {
      console.error('加载会话历史失败:', error);
    }
  };

  // 会话管理方法
  const createNewSession = async (): Promise<string> => {
    try {
      // 调用后端API创建会话
      const sessionInfo = await chatAPI.createSession({ title: '新对话' });

      const newSession: Session = {
        id: sessionInfo.thread_id,
        title: sessionInfo.title,
        messageCount: sessionInfo.message_count,
        lastActiveTime: new Date(sessionInfo.last_active_time),
        createdAt: new Date(sessionInfo.created_at),
        messages: []
      };

      sessions.value.unshift(newSession);
      switchToSession(newSession.id);
      saveSessionsToStorage();
      return newSession.id;
    } catch (error) {
      console.error('创建会话失败:', error);
      // 降级到本地创建
      const sessionId = generateThreadId();
      const newSession: Session = {
        id: sessionId,
        title: '新对话',
        messageCount: 0,
        lastActiveTime: new Date(),
        createdAt: new Date(),
        messages: []
      };

      sessions.value.unshift(newSession);
      switchToSession(sessionId);
      saveSessionsToStorage();
      return sessionId;
    }
  };

  const switchToSession = (sessionId: string) => {
    // 保存当前会话的消息
    if (currentSessionId.value) {
      const currentSession = sessions.value.find(s => s.id === currentSessionId.value);
      if (currentSession) {
        currentSession.messages = [...messages.value];
        currentSession.messageCount = messages.value.length;
        currentSession.lastActiveTime = new Date();
      }
    }

    // 切换到新会话
    const targetSession = sessions.value.find(s => s.id === sessionId);
    if (targetSession) {
      currentSessionId.value = sessionId;
      currentThreadId.value = sessionId;
      messages.value = [...targetSession.messages];

      // 更新最后活跃时间
      targetSession.lastActiveTime = new Date();
      saveSessionsToStorage();
    }
  };

  const deleteSession = async (sessionId: string) => {
    try {
      // 调用后端API删除会话
      await chatAPI.deleteSession(sessionId);
    } catch (error) {
      console.error('删除会话失败:', error);
      // 继续本地删除
    }

    const sessionIndex = sessions.value.findIndex(s => s.id === sessionId);
    if (sessionIndex === -1) return;

    sessions.value.splice(sessionIndex, 1);

    // 如果删除的是当前会话，切换到其他会话或创建新会话
    if (currentSessionId.value === sessionId) {
      if (sessions.value.length > 0) {
        switchToSession(sessions.value[0].id);
      } else {
        await createNewSession();
      }
    }
    saveSessionsToStorage();
  };

  const renameSession = async (sessionId: string, newTitle: string) => {
    try {
      // 调用后端API更新会话
      await chatAPI.updateSession(sessionId, { title: newTitle });

      const session = sessions.value.find(s => s.id === sessionId);
      if (session) {
        session.title = newTitle;
        saveSessionsToStorage();
      }
    } catch (error) {
      console.error('重命名会话失败:', error);
      // 降级到本地更新
      const session = sessions.value.find(s => s.id === sessionId);
      if (session) {
        session.title = newTitle;
        saveSessionsToStorage();
      }
    }
  };

  const updateCurrentSessionTitle = () => {
    if (currentSessionId.value && messages.value.length > 0) {
      const session = sessions.value.find(s => s.id === currentSessionId.value);
      if (session && session.title === '新对话') {
        // 使用第一条用户消息作为标题
        const firstUserMessage = messages.value.find(m => m.role === 'user');
        if (firstUserMessage) {
          const title = firstUserMessage.content.slice(0, 20);
          session.title = title.length < firstUserMessage.content.length ? title + '...' : title;
        }
      }
    }
  };

  // 持久化相关方法
  const saveSessionsToStorage = () => {
    try {
      const sessionsData = sessions.value.map(session => ({
        ...session,
        lastActiveTime: session.lastActiveTime.toISOString(),
        createdAt: session.createdAt.toISOString()
      }));
      localStorage.setItem('chat_sessions', JSON.stringify(sessionsData));
      localStorage.setItem('current_session_id', currentSessionId.value);
    } catch (error) {
      console.error('保存会话数据失败:', error);
    }
  };

  const loadSessionsFromStorage = () => {
    try {
      const sessionsData = localStorage.getItem('chat_sessions');
      const savedCurrentSessionId = localStorage.getItem('current_session_id');

      if (sessionsData) {
        const parsedSessions = JSON.parse(sessionsData);
        sessions.value = parsedSessions.map((session: any) => ({
          ...session,
          lastActiveTime: new Date(session.lastActiveTime),
          createdAt: new Date(session.createdAt)
        }));

        if (savedCurrentSessionId && sessions.value.find(s => s.id === savedCurrentSessionId)) {
          switchToSession(savedCurrentSessionId);
        } else if (sessions.value.length > 0) {
          switchToSession(sessions.value[0].id);
        }
      }
    } catch (error) {
      console.error('加载会话数据失败:', error);
    }
  };

  const reorderSessions = (sessionIds: string[]) => {
    // 根据新的顺序重新排列会话
    const reorderedSessions: Session[] = [];

    sessionIds.forEach(id => {
      const session = sessions.value.find(s => s.id === id);
      if (session) {
        reorderedSessions.push(session);
      }
    });

    // 添加任何遗漏的会话
    sessions.value.forEach(session => {
      if (!sessionIds.includes(session.id)) {
        reorderedSessions.push(session);
      }
    });

    sessions.value = reorderedSessions;
    saveSessionsToStorage();
  };

  // 初始化：加载保存的会话或创建默认会话
  const initializeStore = () => {
    loadSessionsFromStorage();
    if (sessions.value.length === 0) {
      createNewSession();
    }
  };

  // 新增方法
  const setStreaming = (value: boolean) => {
    useStreaming.value = value;
  };

  const createSession = () => {
    createNewSession();
  };

  const selectSession = (sessionId: string) => {
    switchToSession(sessionId);
  };

  const clearCurrentSession = () => {
    clearMessages();
  };

  return {
    // 状态
    messages,
    currentThreadId,
    isLoading,
    isConnected,
    sessions,
    currentSessionId,
    useStreaming,
    startChat,

    // 计算属性
    hasMessages,
    lastMessage,

    // 方法
    addMessage,
    sendMessage,
    connectWebSocket,
    disconnectWebSocket,
    clearMessages,
    loadSessionHistory,
    setStreaming,
    createSession,
    selectSession,
    clearCurrentSession,

    // 会话管理方法
    createNewSession,
    switchToSession,
    deleteSession,
    renameSession,
    reorderSessions,
    initializeStore,
  };
});
