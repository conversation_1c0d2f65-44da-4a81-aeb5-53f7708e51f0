<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>MateChat 前后端集成测试</h1>
    
    <div class="test-section">
        <h2>1. 健康检查测试</h2>
        <button onclick="testHealth()">测试健康检查</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 聊天API测试</h2>
        <input type="text" id="chat-input" placeholder="输入消息..." value="你好，请介绍一下自己">
        <button onclick="testChat()">发送消息</button>
        <div id="chat-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. WebSocket测试</h2>
        <button onclick="connectWebSocket()">连接WebSocket</button>
        <button onclick="disconnectWebSocket()">断开连接</button>
        <input type="text" id="ws-input" placeholder="WebSocket消息..." value="通过WebSocket发送的消息">
        <button onclick="sendWebSocketMessage()">发送WebSocket消息</button>
        <div id="ws-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let websocket = null;

        // 健康检查测试
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                resultDiv.textContent = `✅ 健康检查成功:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.textContent = `❌ 健康检查失败: ${error.message}`;
            }
        }

        // 聊天API测试
        async function testChat() {
            const resultDiv = document.getElementById('chat-result');
            const input = document.getElementById('chat-input');
            const message = input.value.trim();
            
            if (!message) {
                resultDiv.textContent = '❌ 请输入消息';
                return;
            }

            try {
                resultDiv.textContent = '⏳ 发送中...';
                const response = await fetch(`${API_BASE}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                resultDiv.textContent = `✅ 聊天API成功:\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultDiv.textContent = `❌ 聊天API失败: ${error.message}`;
            }
        }

        // WebSocket连接测试
        function connectWebSocket() {
            const resultDiv = document.getElementById('ws-result');
            
            if (websocket) {
                resultDiv.textContent = '⚠️ WebSocket已连接';
                return;
            }

            try {
                const threadId = `test_${Date.now()}`;
                websocket = new WebSocket(`ws://localhost:8000/ws/${threadId}`);
                
                websocket.onopen = () => {
                    resultDiv.textContent = '✅ WebSocket连接成功';
                };

                websocket.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    resultDiv.textContent += `\n📨 收到消息: ${JSON.stringify(data, null, 2)}`;
                };

                websocket.onclose = () => {
                    resultDiv.textContent += '\n🔌 WebSocket连接已关闭';
                    websocket = null;
                };

                websocket.onerror = (error) => {
                    resultDiv.textContent += `\n❌ WebSocket错误: ${error}`;
                };

            } catch (error) {
                resultDiv.textContent = `❌ WebSocket连接失败: ${error.message}`;
            }
        }

        // 断开WebSocket
        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        // 发送WebSocket消息
        function sendWebSocketMessage() {
            const resultDiv = document.getElementById('ws-result');
            const input = document.getElementById('ws-input');
            const message = input.value.trim();

            if (!websocket) {
                resultDiv.textContent += '\n❌ 请先连接WebSocket';
                return;
            }

            if (!message) {
                resultDiv.textContent += '\n❌ 请输入消息';
                return;
            }

            try {
                const data = {
                    type: 'chat',
                    message: message
                };
                websocket.send(JSON.stringify(data));
                resultDiv.textContent += `\n📤 发送消息: ${message}`;
            } catch (error) {
                resultDiv.textContent += `\n❌ 发送失败: ${error.message}`;
            }
        }

        // 页面加载时自动测试健康检查
        window.onload = () => {
            testHealth();
        };
    </script>
</body>
</html>
