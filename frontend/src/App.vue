<template>
  <div id="app">
    <ChatView />
  </div>
</template>

<script setup lang="ts">
import ChatView from '@/components/ChatView.vue';
</script>

<style>
#app {
  height: 100vh;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-tabs {
  display: flex;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.tab-btn {
  padding: 12px 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-size: 14px;
}

.tab-btn.active {
  background: white;
  border-bottom-color: #007bff;
  color: #007bff;
}

.tab-btn:hover {
  background: #e9ecef;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}
</style>
