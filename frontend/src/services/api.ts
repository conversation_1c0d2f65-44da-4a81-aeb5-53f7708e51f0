import axios from 'axios';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
}

export interface ChatRequest {
  message: string;
  thread_id?: string;
  stream?: boolean;
}

export interface ChatResponse {
  message: string;
  thread_id: string;
  timestamp: string;
}

export interface SessionInfo {
  thread_id: string;
  title: string;
  created_at: string;
  last_active_time: string;
  message_count: number;
}

export interface CreateSessionRequest {
  title?: string;
}

export interface UpdateSessionRequest {
  title: string;
}

// 移除文件上传相关接口 - 只使用MateChat官方组件

export class ChatAPI {
  private baseURL = 'http://localhost:8000';
  private client = axios.create({
    baseURL: this.baseURL,
    timeout: 30000,
  });

  // 发送聊天消息
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const response = await this.client.post('/api/chat', request);
    return response.data;
  }

  // SSE流式聊天
  async sendStreamMessage(
    request: ChatRequest,
    onChunk: (chunk: any) => void,
    onComplete?: () => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    try {
      const response = await fetch(`${this.baseURL}/api/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.body) {
        throw new Error('No response body');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            onComplete?.();
            break;
          }

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保留最后一行（可能不完整）

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                onChunk(data);
              } catch (e) {
                console.warn('Failed to parse SSE data:', line);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      onError?.(error as Error);
      throw error;
    }
  }

  // WebSocket连接
  connectWebSocket(threadId: string): WebSocket {
    return new WebSocket(`ws://localhost:8000/ws/${threadId}`);
  }

  // 获取会话历史
  async getSessionHistory(threadId: string): Promise<ChatMessage[]> {
    const response = await this.client.get(`/api/sessions/${threadId}/history`);
    return response.data.history || [];
  }

  // 获取可用工具
  async getAvailableTools(): Promise<any[]> {
    const response = await this.client.get('/api/tools');
    return response.data.tools || [];
  }

  // 会话管理API
  async createSession(request: CreateSessionRequest): Promise<SessionInfo> {
    const response = await this.client.post('/api/sessions', request);
    return response.data;
  }

  async listSessions(userId?: string): Promise<SessionInfo[]> {
    const params = userId ? { user_id: userId } : {};
    const response = await this.client.get('/api/sessions', { params });
    return response.data.sessions || [];
  }

  async updateSession(threadId: string, request: UpdateSessionRequest): Promise<any> {
    const response = await this.client.put(`/api/sessions/${threadId}`, request);
    return response.data;
  }

  async deleteSession(threadId: string): Promise<any> {
    const response = await this.client.delete(`/api/sessions/${threadId}`);
    return response.data;
  }

  // 移除文件上传API - 只使用MateChat官方组件
}

// 流式响应处理类
export class StreamingService {
  private baseURL = 'http://localhost:8000';

  // 创建流式聊天连接
  async createStreamingChat(
    request: ChatRequest,
    callbacks: {
      onStart?: () => void;
      onChunk?: (content: string, isComplete: boolean) => void;
      onComplete?: (fullResponse: string) => void;
      onError?: (error: Error) => void;
    }
  ): Promise<void> {
    let fullResponse = '';
    let currentMessage = '';

    try {
      callbacks.onStart?.();

      await chatAPI.sendStreamMessage(
        request,
        (chunk) => {
          // 处理不同类型的流式数据
          if (chunk.type === 'AIMessage' || chunk.type === 'ai_chunk') {
            const content = chunk.content || '';
            if (content) {
              currentMessage += content;
              fullResponse += content;
              callbacks.onChunk?.(currentMessage, false);
            }
          } else if (chunk.type === 'Error') {
            callbacks.onError?.(new Error(chunk.content || '处理消息时出现错误'));
          }
        },
        () => {
          // 流式响应完成
          callbacks.onChunk?.(currentMessage, true);
          callbacks.onComplete?.(fullResponse);
        },
        (error) => {
          callbacks.onError?.(error);
        }
      );
    } catch (error) {
      callbacks.onError?.(error as Error);
    }
  }

  // 打字机效果显示
  async displayWithTypewriter(
    text: string,
    callback: (displayText: string, isComplete: boolean) => void,
    speed: number = 30 // 每个字符的显示间隔（毫秒）
  ): Promise<void> {
    let displayText = '';

    for (let i = 0; i < text.length; i++) {
      displayText += text[i];
      callback(displayText, i === text.length - 1);

      if (i < text.length - 1) {
        await new Promise(resolve => setTimeout(resolve, speed));
      }
    }
  }
}

export const chatAPI = new ChatAPI();
export const streamingService = new StreamingService();
