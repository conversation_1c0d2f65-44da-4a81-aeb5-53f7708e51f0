# my_project 前端界面

基于Vue3 + TypeScript + MateChat的现代化AI聊天前端界面。

## 🎨 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速的前端构建工具
- **MateChat** - 专业的AI聊天UI组件库
- **Pinia** - Vue状态管理
- **Axios** - HTTP客户端

## 🚀 快速开始

### 开发模式
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 生产构建
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 📁 项目结构

```
src/
├── components/          # Vue组件
│   └── ChatInterface.vue  # 主聊天界面
├── services/           # API服务
│   └── api.ts         # 后端API封装
├── stores/            # Pinia状态管理
│   └── chat.ts        # 聊天状态管理
└── test/              # 测试文件
    └── api-test.html  # API测试页面
```

## 🔧 配置说明

- **开发服务器**: http://localhost:3000
- **后端API**: http://localhost:8000
- **构建输出**: `../static/frontend/`

## 📚 相关文档

- [Vue 3 文档](https://vuejs.org/)
- [MateChat 文档](https://matechat.gitcode.com)
- [Pinia 文档](https://pinia.vuejs.org/)
