import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    open: true,
    host: '0.0.0.0', // 允许外部访问
  },
  build: {
    outDir: '../static/frontend', // 构建输出到后端静态文件目录
    emptyOutDir: true,
  },
})
