#!/bin/bash

# my_project 全栈启动脚本
# 同时启动后端API服务和前端开发服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示启动信息
show_startup_info() {
    echo -e "${BLUE}================================================================================${NC}"
    echo -e "${PURPLE}🚀 my_project 全栈AI智能助手系统${NC}"
    echo -e "${BLUE}================================================================================${NC}"
    echo -e "${CYAN}📋 服务信息:${NC}"
    echo -e "  🎨 前端界面: http://localhost:3000"
    echo -e "  🔧 后端API: http://localhost:8000"
    echo -e "  📚 API文档: http://localhost:8000/docs"
    echo -e "  ❤️ 健康检查: http://localhost:8000/health"
    echo ""
    echo -e "${CYAN}🎯 功能特性:${NC}"
    echo -e "  • 现代化AI聊天界面 (MateChat)"
    echo -e "  • 实时WebSocket通信"
    echo -e "  • 智谱GLM-4-Flash模型"
    echo -e "  • 会话历史持久化"
    echo -e "  • 响应式设计"
    echo ""
    echo -e "${CYAN}⚡ 快速体验:${NC}"
    echo -e "  1. 浏览器访问: http://localhost:3000"
    echo -e "  2. 开始与AI助手对话"
    echo ""
    echo -e "${YELLOW}🔥 正在启动服务...${NC}"
    echo -e "${BLUE}================================================================================${NC}"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}❌ $1 未安装${NC}"
        return 1
    else
        echo -e "${GREEN}✅ $1 已安装${NC}"
        return 0
    fi
}

# 检查环境
check_environment() {
    echo -e "${CYAN}🔍 检查环境...${NC}"
    
    if ! check_command "uv"; then
        echo -e "${RED}请先安装 uv: https://docs.astral.sh/uv/getting-started/installation/${NC}"
        exit 1
    fi
    
    if ! check_command "node"; then
        echo -e "${RED}请先安装 Node.js: https://nodejs.org/${NC}"
        exit 1
    fi
    
    if ! check_command "npm"; then
        echo -e "${RED}请先安装 npm (通常随Node.js一起安装)${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境检查通过${NC}"
}

# 安装前端依赖
install_frontend_deps() {
    if [ ! -d "frontend/node_modules" ]; then
        echo -e "${YELLOW}📦 正在安装前端依赖...${NC}"
        cd frontend
        npm install
        cd ..
        echo -e "${GREEN}✅ 前端依赖安装完成${NC}"
    else
        echo -e "${GREEN}✅ 前端依赖已存在${NC}"
    fi
}

# 清理函数
cleanup() {
    echo -e "\n${YELLOW}🛑 正在停止服务...${NC}"
    
    # 杀死后台进程
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # 等待进程结束
    sleep 2
    
    echo -e "${GREEN}👋 所有服务已停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    # 显示启动信息
    show_startup_info
    
    # 检查环境
    check_environment
    
    # 安装前端依赖
    install_frontend_deps
    
    # 启动后端服务
    echo -e "${CYAN}🚀 启动后端API服务...${NC}"
    uv run start_web.py &
    BACKEND_PID=$!
    
    # 等待后端启动
    echo -e "${YELLOW}⏳ 等待后端服务启动...${NC}"
    sleep 5
    
    # 启动前端服务
    echo -e "${CYAN}🎨 启动前端开发服务器...${NC}"
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    # 等待前端启动
    sleep 3
    
    echo ""
    echo -e "${GREEN}✅ 全栈服务启动成功!${NC}"
    echo -e "${PURPLE}🌐 前端界面: http://localhost:3000${NC}"
    echo -e "${PURPLE}🔧 后端API: http://localhost:8000${NC}"
    echo ""
    echo -e "${YELLOW}按 Ctrl+C 停止所有服务${NC}"
    
    # 等待用户中断
    wait
}

# 运行主函数
main
