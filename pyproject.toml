[project]
name = "my-project"
version = "2.0.0"
description = "基于LangChain和LangGraph构建的智能代理系统，支持CLI和Web API混合模式"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # 核心LangChain和LangGraph依赖
    "langchain>=0.3.0",
    "langgraph[sqlite]>=0.2.0",
    "langchain-openai>=0.2.0",
    "langchain-mcp-adapters>=0.1.0",
    "langchain-core>=0.3.0",
    "aiosqlite>=0.19.0",
    "langgraph-checkpoint-sqlite>=2.0.10",
    # Web API相关依赖
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "websockets>=12.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    # 测试相关依赖
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0",
]

[tool.uv]
index-url = "https://mirrors.aliyun.com/pypi/simple/"
allow-insecure-host = ["mirrors.aliyun.com"]
