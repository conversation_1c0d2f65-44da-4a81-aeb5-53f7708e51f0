#!/usr/bin/env python3
"""
Web API 启动脚本
专门用于启动Web API服务的入口文件
"""

import asyncio
import sys
import os
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入Web API应用
from interfaces.web_api import create_app
import uvicorn


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在关闭Web API服务器...")
    sys.exit(0)


def show_startup_info():
    """显示启动信息"""
    print("\n🚀 启动混合架构Web API服务器...")
    print("=" * 60)
    print("📋 服务信息:")
    print("  - CLI模式: uv run main.py")
    print("  - Web API: http://localhost:8000")
    print("  - API文档: http://localhost:8000/docs")
    print("  - 健康检查: http://localhost:8000/health")
    print("  - WebSocket: ws://localhost:8000/ws/{thread_id}")
    print("")
    print("🎯 可用的API端点:")
    print("  GET  /health - 健康检查")
    print("  POST /api/chat - 聊天接口")
    print("  POST /api/chat/stream - 流式聊天")
    print("  GET  /api/sessions/{thread_id}/history - 会话历史")
    print("  GET  /api/tools - 可用工具列表")
    print("  WS   /ws/{thread_id} - WebSocket实时通信")
    print("")
    print("✨ 现在可以通过以下方式体验:")
    print("  1. 浏览器访问: http://localhost:8000/docs")
    print("  2. CLI命令: uv run main.py")
    print("  3. API调用示例:")
    print("     curl -X POST http://localhost:8000/api/chat \\")
    print("          -H 'Content-Type: application/json' \\")
    print("          -d '{\"message\": \"你好，请介绍一下这个系统\"}'")
    print("")
    print("🔥 正在启动服务器...")


async def start_server():
    """启动Web API服务器"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 显示启动信息
    show_startup_info()
    
    try:
        # 创建FastAPI应用
        app = create_app()
        
        # 配置uvicorn服务器
        config = uvicorn.Config(
            app, 
            host='0.0.0.0', 
            port=8000, 
            log_level='info',
            reload=False  # 生产模式不使用reload
        )
        
        # 启动服务器
        server = uvicorn.Server(config)
        await server.serve()
        
    except Exception as e:
        print(f"❌ 启动Web API服务器失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def main():
    """主函数"""
    try:
        # 运行异步服务器
        asyncio.run(start_server())
    except KeyboardInterrupt:
        print("\n👋 Web API服务器已关闭")
    except Exception as e:
        print(f"❌ 服务器运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
