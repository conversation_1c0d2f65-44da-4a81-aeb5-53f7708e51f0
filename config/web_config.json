{"web": {"enabled": true, "host": "0.0.0.0", "port": 8000, "debug": false, "cors_origins": ["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"], "max_connections": 100, "timeout": 300, "rate_limit": {"enabled": true, "requests_per_minute": 60, "burst_size": 10}, "authentication": {"enabled": false, "jwt_secret": "your-secret-key-here", "token_expire_hours": 24}, "websocket": {"ping_interval": 20, "ping_timeout": 10, "close_timeout": 10}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file": "logs/web_api.log"}}}