"""
核心智能体逻辑
从main.py中提取的核心组件，支持CLI和Web API共享使用
"""

import asyncio
import json
import os
import uuid
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path

from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langgraph.graph import END, StateGraph, MessagesState
from langgraph.prebuilt import ToolNode

# 导入持久化模块
import aiosqlite
try:
    from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
    SQLITE_AVAILABLE = True
except ImportError:
    from langgraph.checkpoint.memory import MemorySaver
    SQLITE_AVAILABLE = False

# 导入配置管理器和错误处理器
from services.config_manager import config_manager
from services.error_handler import error_handler, async_error_handler_decorator


# 使用官方推荐的 MessagesState
class AgentState(MessagesState):
    """使用官方的 MessagesState，包含 messages 字段"""
    pass


class EnhancedSessionManager:
    """增强的会话管理器 - 基于SimpleSessionManager扩展"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or config_manager.get_persistence_config()
        session_config = config_manager.get_session_config()

        self.current_thread_id = None
        self.current_user_id = None
        self.default_user_id = session_config.get("default_user_prefix", "default_user")
        self.session_timeout_hours = session_config.get("session_timeout_hours", 24)
        self.max_sessions_per_user = session_config.get("max_sessions_per_user", 10)
        self.auto_cleanup_enabled = session_config.get("auto_cleanup_enabled", True)

        # 会话存储
        self.sessions: Dict[str, Dict[str, Any]] = {}
        self.user_sessions: Dict[str, List[str]] = {}

    def create_session(self, user_id: Optional[str] = None) -> str:
        """创建新会话"""
        if user_id is None:
            user_id = self.default_user_id

        thread_id = f"{user_id}_{uuid.uuid4().hex[:8]}"
        
        # 存储会话信息
        self.sessions[thread_id] = {
            'user_id': user_id,
            'created_at': asyncio.get_event_loop().time(),
            'last_accessed': asyncio.get_event_loop().time()
        }
        
        # 更新用户会话列表
        if user_id not in self.user_sessions:
            self.user_sessions[user_id] = []
        self.user_sessions[user_id].append(thread_id)
        
        # 检查会话数量限制
        if len(self.user_sessions[user_id]) > self.max_sessions_per_user:
            # 删除最旧的会话
            oldest_thread = self.user_sessions[user_id].pop(0)
            if oldest_thread in self.sessions:
                del self.sessions[oldest_thread]

        self.current_thread_id = thread_id
        self.current_user_id = user_id
        
        return thread_id

    def get_session_config(self, thread_id: str) -> Dict[str, Any]:
        """获取LangGraph标准的会话配置"""
        # 更新最后访问时间
        if thread_id in self.sessions:
            self.sessions[thread_id]['last_accessed'] = asyncio.get_event_loop().time()
        
        return {"configurable": {"thread_id": thread_id}}

    def resume_session(self, thread_id: str) -> str:
        """恢复会话"""
        if thread_id in self.sessions:
            self.current_thread_id = thread_id
            self.current_user_id = self.sessions[thread_id]['user_id']
            self.sessions[thread_id]['last_accessed'] = asyncio.get_event_loop().time()
            return thread_id
        else:
            # 如果会话不存在，创建新会话
            return self.create_session()

    def clear_current_session(self) -> str:
        """清除当前会话，创建新会话"""
        return self.create_session(self.current_user_id)

    def list_user_sessions(self, user_id: str) -> List[str]:
        """列出用户的所有会话"""
        return self.user_sessions.get(user_id, [])

    def cleanup_expired_sessions(self):
        """清理过期会话"""
        if not self.auto_cleanup_enabled:
            return

        current_time = asyncio.get_event_loop().time()
        timeout_seconds = self.session_timeout_hours * 3600
        expired_sessions = []

        for thread_id, session_info in self.sessions.items():
            if current_time - session_info['last_accessed'] > timeout_seconds:
                expired_sessions.append(thread_id)

        for thread_id in expired_sessions:
            user_id = self.sessions[thread_id]['user_id']
            del self.sessions[thread_id]
            if user_id in self.user_sessions and thread_id in self.user_sessions[user_id]:
                self.user_sessions[user_id].remove(thread_id)


class AgentCore:
    """核心智能体类 - 可被CLI和Web API共享使用"""
    
    def __init__(self):
        self.app = None
        self.tools = None
        self.checkpointer = None
        self.session_manager = None
        self._initialized = False
        self.llm = None
        self.llm_with_tools = None

    @async_error_handler_decorator(error_handler)
    async def initialize(self):
        """初始化智能体"""
        if self._initialized:
            return

        # 创建检查点存储器
        self.checkpointer = await self._create_checkpointer()
        
        # 创建会话管理器
        self.session_manager = EnhancedSessionManager()

        # 加载LLM和工具
        await self._load_llm_and_tools()

        # 构建工作流
        self.app = await self._build_workflow()
        
        self._initialized = True

    async def _create_checkpointer(self):
        """创建检查点存储器"""
        if not config_manager.is_persistence_enabled():
            from langgraph.checkpoint.memory import MemorySaver
            return MemorySaver()

        backend = config_manager.get_persistence_backend()
        
        if backend == "sqlite" and SQLITE_AVAILABLE:
            db_path = config_manager.get_database_path()
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            
            conn = await aiosqlite.connect(db_path)
            checkpointer = AsyncSqliteSaver(conn)
            return checkpointer
        elif backend == "memory":
            from langgraph.checkpoint.memory import MemorySaver
            return MemorySaver()
        else:
            from langgraph.checkpoint.memory import MemorySaver
            return MemorySaver()

    async def _load_llm_and_tools(self):
        """加载LLM和工具"""
        # 动态导入以避免循环依赖
        from utils.llm_loader import load_llm_from_config
        from utils.mcp_loader import load_mcp_tools_from_config

        # 加载LLM
        self.llm = load_llm_from_config("config/llm_config.json")

        # 加载MCP工具
        _, self.tools = await load_mcp_tools_from_config("config/mcp_config.json")
        
        # 将工具绑定到LLM
        self.llm_with_tools = self.llm.bind_tools(self.tools)

    async def _build_workflow(self):
        """构建LangGraph工作流"""
        # 定义节点函数
        def call_model(state: AgentState):
            """调用模型节点"""
            messages = state["messages"]

            try:
                response = self.llm_with_tools.invoke(messages)

                # 确保响应不为空
                if not response.content and not (hasattr(response, 'tool_calls') and response.tool_calls):
                    response = AIMessage(content="抱歉，我无法处理这个请求。请重新尝试。")

                return {"messages": [response]}
            except Exception as e:
                error_response = AIMessage(content=f"抱歉，处理请求时出现错误: {str(e)}")
                return {"messages": [error_response]}

        # 创建工具节点
        tool_node = ToolNode(self.tools)

        def should_continue(state: AgentState):
            """判断是否继续执行"""
            messages = state["messages"]
            last_message = messages[-1]

            if isinstance(last_message, AIMessage) and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                return "tools"
            return END

        # 构建图
        workflow = StateGraph(AgentState)

        # 添加节点
        workflow.add_node("agent", call_model)
        workflow.add_node("tools", tool_node)

        # 设置入口点
        workflow.set_entry_point("agent")

        # 添加条件边
        workflow.add_conditional_edges(
            "agent",
            should_continue,
            ["tools", END]
        )

        # 添加从工具节点返回到Agent节点的边
        workflow.add_edge("tools", "agent")

        # 编译图时集成检查点
        app = workflow.compile(checkpointer=self.checkpointer)
        
        return app

    @async_error_handler_decorator(error_handler)
    async def process_message(self, message: str, thread_id: Optional[str] = None, 
                            user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        处理单个消息
        
        Args:
            message: 用户消息
            thread_id: 会话ID，如果为None则创建新会话
            user_id: 用户ID
            
        Returns:
            包含响应和会话信息的字典
        """
        if not self._initialized:
            await self.initialize()

        # 处理会话
        if thread_id is None:
            thread_id = self.session_manager.create_session(user_id)
        else:
            thread_id = self.session_manager.resume_session(thread_id)

        # 准备输入
        inputs = {"messages": [HumanMessage(content=message)]}
        config = self.session_manager.get_session_config(thread_id)

        # 执行Agent
        result_messages = []
        async for output in self.app.astream(inputs, config=config, stream_mode="values"):
            messages = output["messages"]
            result_messages = messages

        # 提取最终响应
        final_response = ""
        if result_messages:
            last_message = result_messages[-1]
            if isinstance(last_message, AIMessage):
                final_response = last_message.content

        return {
            "response": final_response,
            "thread_id": thread_id,
            "user_id": user_id or self.session_manager.default_user_id,
            "message_count": len(result_messages)
        }

    async def stream_message(self, message: str, thread_id: Optional[str] = None,
                           user_id: Optional[str] = None):
        """
        流式处理消息

        Args:
            message: 用户消息
            thread_id: 会话ID
            user_id: 用户ID

        Yields:
            流式响应数据
        """
        try:
            if not self._initialized:
                await self.initialize()

            # 处理会话
            if thread_id is None:
                thread_id = self.session_manager.create_session(user_id)
            else:
                thread_id = self.session_manager.resume_session(thread_id)

            # 准备输入
            inputs = {"messages": [HumanMessage(content=message)]}
            config = self.session_manager.get_session_config(thread_id)

            # 流式执行
            async for output in self.app.astream(inputs, config=config, stream_mode="values"):
                messages = output["messages"]
                last_message = messages[-1]

                yield {
                    "type": type(last_message).__name__,
                    "content": getattr(last_message, 'content', ''),
                    "tool_calls": getattr(last_message, 'tool_calls', None),
                    "thread_id": thread_id,
                    "message_count": len(messages)
                }
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"message": message, "thread_id": thread_id})
            yield {
                "type": "Error",
                "content": f"处理消息时出现错误: {custom_error.message}",
                "tool_calls": None,
                "thread_id": thread_id,
                "message_count": 0
            }

    async def get_session_history(self, thread_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取会话历史"""
        if not self._initialized:
            await self.initialize()

        try:
            config = self.session_manager.get_session_config(thread_id)
            state = await self.app.aget_state(config)
            
            if state.values and "messages" in state.values:
                messages = state.values["messages"][-limit:]  # 限制返回数量
                
                history = []
                for msg in messages:
                    history.append({
                        "type": type(msg).__name__,
                        "content": getattr(msg, 'content', ''),
                        "tool_calls": getattr(msg, 'tool_calls', None),
                        "name": getattr(msg, 'name', None)
                    })
                
                return history
            else:
                return []
        except Exception as e:
            error_handler.handle_error(e, {"thread_id": thread_id})
            return []

    def get_available_tools(self) -> List[Dict[str, str]]:
        """获取可用工具列表"""
        if not self.tools:
            return []
        
        return [
            {
                "name": tool.name,
                "description": tool.description
            }
            for tool in self.tools
        ]

    def create_session(self, user_id: Optional[str] = None) -> str:
        """创建新会话"""
        if not self.session_manager:
            self.session_manager = EnhancedSessionManager()
        return self.session_manager.create_session(user_id)

    def list_user_sessions(self, user_id: str) -> List[str]:
        """列出用户会话"""
        if not self.session_manager:
            return []
        return self.session_manager.list_user_sessions(user_id)


# 全局智能体核心实例
agent_core = AgentCore()
