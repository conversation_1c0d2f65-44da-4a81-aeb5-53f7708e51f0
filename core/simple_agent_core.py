"""
简化的智能体核心 - 用于Web API测试
不依赖MCP工具，使用基本的LangGraph功能
"""

import asyncio
import json
import os
import uuid
from typing import Dict, Any, Optional, List
from pathlib import Path

from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import END, StateGraph, MessagesState
from langgraph.checkpoint.memory import MemorySaver

# 导入配置管理器和错误处理器
from .config_manager import config_manager
from .error_handler import error_handler


class SimpleSessionManager:
    """简化的会话管理器"""
    
    def __init__(self):
        self.sessions = {}
    
    def get_or_create_session(self, thread_id: str) -> Dict[str, Any]:
        """获取或创建会话"""
        if thread_id not in self.sessions:
            self.sessions[thread_id] = {
                "thread_id": thread_id,
                "created_at": asyncio.get_event_loop().time(),
                "message_count": 0
            }
        return self.sessions[thread_id]
    
    def update_session(self, thread_id: str, **kwargs):
        """更新会话信息"""
        if thread_id in self.sessions:
            self.sessions[thread_id].update(kwargs)


class SimpleAgentCore:
    """简化的智能体核心类"""
    
    def __init__(self):
        self.app = None
        self.checkpointer = None
        self.session_manager = None
        self._initialized = False
    
    async def initialize(self):
        """初始化智能体"""
        if self._initialized:
            return
        
        print("初始化简化智能体核心...")
        
        # 创建检查点存储器
        self.checkpointer = MemorySaver()
        
        # 创建会话管理器
        self.session_manager = SimpleSessionManager()
        
        # 构建工作流
        self.app = await self._build_workflow()
        
        self._initialized = True
        print("✅ 简化智能体核心初始化完成")
    
    async def _build_workflow(self):
        """构建简化的工作流"""
        
        def agent_node(state: MessagesState):
            """简化的智能体节点 - 直接回复用户消息"""
            messages = state["messages"]
            last_message = messages[-1]
            
            # 简单的回复逻辑
            if isinstance(last_message, HumanMessage):
                content = last_message.content
                
                # 简单的回复模板
                if "你好" in content or "hello" in content.lower():
                    response = "你好！我是LangGraph智能助手。我现在运行在简化模式下，可以进行基本对话。"
                elif "时间" in content:
                    import datetime
                    response = f"当前时间是：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                elif "功能" in content or "能力" in content:
                    response = "我目前运行在简化模式下，支持基本对话功能。完整版本将包含35个MCP工具和高级功能。"
                elif "测试" in content:
                    response = "Web API测试成功！✅ 我可以正常接收和回复消息。"
                else:
                    response = f"我收到了您的消息：「{content}」。我现在运行在简化模式下，可以进行基本对话。如需完整功能，请使用完整版本。"
                
                ai_message = AIMessage(content=response)
                return {"messages": [ai_message]}
            
            return {"messages": []}
        
        # 创建状态图
        workflow = StateGraph(MessagesState)
        
        # 添加节点
        workflow.add_node("agent", agent_node)
        
        # 设置入口点
        workflow.set_entry_point("agent")
        
        # 添加结束边
        workflow.add_edge("agent", END)
        
        # 编译图时集成检查点
        app = workflow.compile(checkpointer=self.checkpointer)
        
        return app
    
    async def process_message(self, message: str, thread_id: Optional[str] = None) -> str:
        """处理单个消息"""
        try:
            if not self._initialized:
                await self.initialize()
            
            # 生成thread_id（如果未提供）
            if not thread_id:
                thread_id = f"simple_user_{uuid.uuid4().hex[:8]}"
            
            # 更新会话信息
            session = self.session_manager.get_or_create_session(thread_id)
            session["message_count"] += 1
            
            # 创建配置
            config = {"configurable": {"thread_id": thread_id}}
            
            # 调用图
            result = await self.app.ainvoke(
                {"messages": [HumanMessage(content=message)]},
                config=config
            )
            
            # 提取AI回复
            if result and "messages" in result and result["messages"]:
                last_message = result["messages"][-1]
                if isinstance(last_message, AIMessage):
                    return last_message.content
            
            return "抱歉，我无法处理您的消息。"
            
        except Exception as e:
            error_handler.handle_error(e, {"message": message, "thread_id": thread_id})
            return f"处理消息时发生错误：{str(e)}"
    
    async def stream_message(self, message: str, thread_id: Optional[str] = None):
        """流式处理消息"""
        try:
            if not self._initialized:
                await self.initialize()
            
            # 生成thread_id（如果未提供）
            if not thread_id:
                thread_id = f"simple_user_{uuid.uuid4().hex[:8]}"
            
            # 更新会话信息
            session = self.session_manager.get_or_create_session(thread_id)
            session["message_count"] += 1
            
            # 创建配置
            config = {"configurable": {"thread_id": thread_id}}
            
            # 先获取完整回复
            result = await self.app.ainvoke(
                {"messages": [HumanMessage(content=message)]},
                config=config
            )
            
            # 提取AI回复
            if result and "messages" in result and result["messages"]:
                last_message = result["messages"][-1]
                if isinstance(last_message, AIMessage):
                    response = last_message.content
                    
                    # 模拟流式输出 - 逐字符输出
                    for char in response:
                        yield char
                        await asyncio.sleep(0.02)  # 模拟打字效果
                    return
            
            yield "抱歉，我无法处理您的消息。"
            
        except Exception as e:
            error_handler.handle_error(e, {"message": message, "thread_id": thread_id})
            yield f"处理消息时发生错误：{str(e)}"
    
    async def get_session_history(self, thread_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """获取会话历史"""
        try:
            if not self._initialized:
                await self.initialize()
            
            # 从checkpointer获取历史
            config = {"configurable": {"thread_id": thread_id}}
            
            # 获取当前状态
            try:
                state = await self.app.aget_state(config)
                if state and state.values and "messages" in state.values:
                    messages = state.values["messages"]
                    
                    # 转换为字典格式
                    history = []
                    for msg in messages[-limit:]:
                        if isinstance(msg, HumanMessage):
                            history.append({
                                "role": "user",
                                "content": msg.content,
                                "timestamp": getattr(msg, 'timestamp', None)
                            })
                        elif isinstance(msg, AIMessage):
                            history.append({
                                "role": "assistant", 
                                "content": msg.content,
                                "timestamp": getattr(msg, 'timestamp', None)
                            })
                    
                    return history
            except Exception:
                pass
            
            return []
            
        except Exception as e:
            error_handler.handle_error(e, {"thread_id": thread_id})
            return []
    
    def get_available_tools(self) -> List[Dict[str, str]]:
        """获取可用工具列表"""
        return [
            {
                "name": "basic_chat",
                "description": "基本对话功能"
            },
            {
                "name": "time_query", 
                "description": "查询当前时间"
            },
            {
                "name": "capability_info",
                "description": "查询系统功能信息"
            }
        ]


# 创建全局实例
simple_agent_core = SimpleAgentCore()
