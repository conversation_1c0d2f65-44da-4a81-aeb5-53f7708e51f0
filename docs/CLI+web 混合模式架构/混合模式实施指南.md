# LangGraph 智能体系统混合模式实施指南

## 📋 概述

本指南提供了将现有命令行项目改造为混合模式（CLI + Web API）的详细实施步骤，确保现有功能完全兼容的同时添加Web API支持。

## 🎯 实施目标

1. **零破坏性改造**：现有命令行功能完全保持不变
2. **渐进式升级**：可以逐步启用Web API功能
3. **代码复用**：最大化复用现有的智能体逻辑
4. **配置驱动**：通过配置文件控制启用的功能

## 📅 实施计划

### Phase 1: 核心重构 (第1周)

#### 步骤1: 创建新的目录结构
```bash
# 在项目根目录执行
mkdir -p core services interfaces/cli interfaces/web/api interfaces/web/websocket interfaces/web/middleware config
```

#### 步骤2: 提取核心逻辑
创建 `core/agent_core.py`：

```python
# core/agent_core.py
"""
从 main.py 提取的核心智能体逻辑
"""

import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from langgraph.graph import END, StateGraph, MessagesState
from langgraph.prebuilt import ToolNode

from llm_loader import load_llm_from_config
from mcp_loader import load_mcp_tools_from_config

class AgentCore:
    """核心智能体类 - 可被CLI和Web API共享使用"""
    
    def __init__(self):
        self.app = None
        self.tools = None
        self.checkpointer = None
        self._initialized = False
    
    async def initialize(self):
        """初始化智能体 - 复用main.py的initialize_agent逻辑"""
        if self._initialized:
            return
        
        # 这里复制main.py中initialize_agent的核心逻辑
        # 但是移除CLI相关的print语句，改为返回状态
        
        from main import create_checkpointer, load_persistence_config
        
        persistence_config = load_persistence_config()
        self.checkpointer = await create_checkpointer(persistence_config)
        
        llm = load_llm_from_config("llm_config.json")
        _, self.tools = await load_mcp_tools_from_config("mcp_config.json")
        
        self.app = await self._build_workflow(llm, self.tools)
        self._initialized = True
    
    async def _build_workflow(self, llm, tools):
        """构建工作流 - 复用main.py的逻辑"""
        # 这里复制main.py中的工作流构建逻辑
        llm_with_tools = llm.bind_tools(tools)
        
        def call_model(state):
            messages = state["messages"]
            try:
                response = llm_with_tools.invoke(messages)
                if not response.content and not (hasattr(response, 'tool_calls') and response.tool_calls):
                    response = AIMessage(content="抱歉，我无法处理这个请求。请重新尝试。")
                return {"messages": [response]}
            except Exception as e:
                error_response = AIMessage(content=f"抱歉，处理请求时出现错误: {str(e)}")
                return {"messages": [error_response]}
        
        tool_node = ToolNode(tools)
        
        def should_continue(state):
            messages = state["messages"]
            last_message = messages[-1]
            if isinstance(last_message, AIMessage) and hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                return "tools"
            return END
        
        workflow = StateGraph(MessagesState)
        workflow.add_node("agent", call_model)
        workflow.add_node("tools", tool_node)
        workflow.set_entry_point("agent")
        workflow.add_conditional_edges("agent", should_continue, ["tools", END])
        workflow.add_edge("tools", "agent")
        
        return workflow.compile(checkpointer=self.checkpointer)
    
    async def process_message_stream(self, message: str, session_config: Dict[str, Any]):
        """处理消息并返回流式结果"""
        if not self._initialized:
            await self.initialize()
        
        inputs = {"messages": [HumanMessage(content=message)]}
        
        async for output in self.app.astream(inputs, config=session_config, stream_mode="values"):
            yield output
    
    async def get_session_history(self, session_config: Dict[str, Any]):
        """获取会话历史"""
        if not self._initialized:
            await self.initialize()
        
        try:
            state = await self.app.aget_state(session_config)
            return state.values.get("messages", []) if state.values else []
        except Exception as e:
            return []
```

#### 步骤3: 创建配置管理
创建 `config/app_config.json`：

```json
{
  "app": {
    "name": "LangGraph智能体系统",
    "version": "2.0.0",
    "mode": "hybrid"
  },
  "interfaces": {
    "cli": {
      "enabled": true,
      "default_user": "cli_user"
    },
    "web": {
      "enabled": false,
      "host": "0.0.0.0",
      "port": 8000,
      "cors_origins": ["http://localhost:3000"],
      "auth_required": false
    }
  },
  "features": {
    "multi_agent": {
      "enabled": false
    },
    "real_time_streaming": true,
    "session_isolation": true
  }
}
```

#### 步骤4: 重构现有main.py
创建 `main_v2.py` (保留原main.py作为备份)：

```python
# main_v2.py
"""
重构后的命令行入口 - 使用新的核心架构
"""

import asyncio
from core.agent_core import AgentCore
from main import SimpleSessionManager, load_persistence_config

async def main():
    """主函数 - 使用新架构但保持相同的CLI体验"""
    try:
        # 初始化核心组件
        agent_core = AgentCore()
        await agent_core.initialize()
        
        # 创建会话管理器 (复用现有逻辑)
        persistence_config = load_persistence_config()
        session_manager = SimpleSessionManager(persistence_config)
        
        # 启动CLI交互循环 (复用现有逻辑)
        await cli_interactive_loop(agent_core, session_manager)
        
    except Exception as e:
        print(f"\n💥 初始化失败: {e}")
        import traceback
        traceback.print_exc()

async def cli_interactive_loop(agent_core, session_manager):
    """CLI交互循环 - 复用main.py的逻辑但使用新的核心"""
    # 这里复制main.py中的interactive_loop_with_persistence逻辑
    # 但是调用agent_core的方法而不是直接调用app
    
    print("\n🤖 LangGraph Agent 交互式助手 (混合架构版本)")
    print("=" * 60)
    
    thread_id = session_manager.create_session()
    
    while True:
        try:
            user_input = input("\n💬 请输入您的问题: ").strip()
            
            if not user_input:
                continue
                
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            elif user_input.lower() == 'new':
                thread_id = session_manager.clear_current_session()
                print("✅ 已开始新对话")
                continue
            
            # 使用新的核心处理消息
            config = session_manager.get_session_config(thread_id)
            
            print(f"\n🚀 开始处理查询: '{user_input}'")
            async for output in agent_core.process_message_stream(user_input, config):
                # 处理输出 (复用main.py的显示逻辑)
                messages = output["messages"]
                last_message = messages[-1]
                
                if isinstance(last_message, HumanMessage):
                    print(f"👤 用户: {last_message.content}")
                elif isinstance(last_message, AIMessage):
                    if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                        print(f"🤖 AI -> 调用工具: {[tc['name'] for tc in last_message.tool_calls]}")
                    else:
                        print(f"🤖 AI -> 最终回复:\n{last_message.content}")
                        
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"\n💥 发生错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
```

### Phase 2: Web API 实现 (第2-3周)

#### 步骤5: 安装Web依赖
更新 `pyproject.toml`：

```toml
[project]
dependencies = [
    # 现有依赖...
    "langchain>=0.3.0",
    "langgraph[sqlite]>=0.2.0",
    "langchain-openai>=0.2.0",
    "langchain-mcp-adapters>=0.1.0",
    "langchain-core>=0.3.0",
    "aiosqlite>=0.19.0",
    "langgraph-checkpoint-sqlite>=2.0.10",
    # 新增Web API依赖
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "websockets>=12.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    # 测试依赖
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.24.0"
]
```

#### 步骤6: 创建Web API服务
创建 `interfaces/web/api/sessions.py`：

```python
# interfaces/web/api/sessions.py
"""
会话管理API
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

router = APIRouter()

class CreateSessionRequest(BaseModel):
    title: Optional[str] = ""
    mode: str = "single_agent"
    user_id: Optional[str] = None

class SessionResponse(BaseModel):
    id: str
    title: str
    mode: str
    created_at: datetime
    status: str

@router.post("/sessions", response_model=dict)
async def create_session(request: CreateSessionRequest):
    """创建新会话"""
    # 这里会调用session_manager.create_session
    # 返回标准的API响应格式
    pass

@router.get("/sessions", response_model=dict)
async def get_sessions(user_id: Optional[str] = None):
    """获取会话列表"""
    # 这里会调用session_manager.get_user_sessions
    pass

@router.get("/sessions/{session_id}", response_model=dict)
async def get_session(session_id: str):
    """获取会话详情"""
    pass

@router.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """删除会话"""
    pass
```

#### 步骤7: 创建WebSocket处理
创建 `interfaces/web/websocket/chat_handler.py`：

```python
# interfaces/web/websocket/chat_handler.py
"""
WebSocket聊天处理器
"""

from fastapi import WebSocket, WebSocketDisconnect
import json
import asyncio
from typing import Dict
from core.agent_core import AgentCore

class ChatWebSocketHandler:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.agent_core = AgentCore()
    
    async def connect(self, websocket: WebSocket, session_id: str):
        await websocket.accept()
        self.active_connections[session_id] = websocket
        
        # 确保agent_core已初始化
        if not self.agent_core._initialized:
            await self.agent_core.initialize()
    
    def disconnect(self, session_id: str):
        if session_id in self.active_connections:
            del self.active_connections[session_id]
    
    async def handle_message(self, session_id: str, data: str):
        try:
            message_data = json.loads(data)
            
            if message_data.get("type") == "user_message":
                content = message_data.get("content")
                
                # 处理用户消息
                config = {"configurable": {"thread_id": session_id}}
                
                async for output in self.agent_core.process_message_stream(content, config):
                    await self.send_message(session_id, {
                        "type": "stream_chunk",
                        "data": output
                    })
                    
        except Exception as e:
            await self.send_message(session_id, {
                "type": "error",
                "error": str(e)
            })
    
    async def send_message(self, session_id: str, message: dict):
        if session_id in self.active_connections:
            websocket = self.active_connections[session_id]
            await websocket.send_text(json.dumps(message))
```

#### 步骤8: 创建Web主程序
创建 `web_main.py`：

```python
# web_main.py
"""
Web API 入口
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json

from interfaces.web.api import sessions
from interfaces.web.websocket.chat_handler import ChatWebSocketHandler

# 加载配置
with open("config/app_config.json", "r", encoding="utf-8") as f:
    app_config = json.load(f)

web_config = app_config["interfaces"]["web"]

app = FastAPI(
    title="LangGraph智能体API",
    description="智能体系统Web API",
    version=app_config["app"]["version"]
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=web_config["cors_origins"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(sessions.router, prefix="/api", tags=["会话"])

# WebSocket管理器
websocket_handler = ChatWebSocketHandler()

@app.websocket("/ws/sessions/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    await websocket_handler.connect(websocket, session_id)
    try:
        while True:
            data = await websocket.receive_text()
            await websocket_handler.handle_message(session_id, data)
    except WebSocketDisconnect:
        websocket_handler.disconnect(session_id)

@app.get("/")
async def root():
    return {"message": "LangGraph智能体API服务", "version": app_config["app"]["version"]}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(
        "web_main:app",
        host=web_config["host"],
        port=web_config["port"],
        reload=True
    )
```

### Phase 3: 混合模式 (第3-4周)

#### 步骤9: 创建混合启动器
创建 `hybrid_main.py`：

```python
# hybrid_main.py
"""
混合模式启动器 - 同时支持CLI和Web API
"""

import asyncio
import threading
import json
from main_v2 import main as cli_main
from web_main import app, web_config
import uvicorn

async def start_hybrid_mode():
    """启动混合模式"""
    # 加载配置
    with open("config/app_config.json", "r", encoding="utf-8") as f:
        config = json.load(f)
    
    cli_enabled = config["interfaces"]["cli"]["enabled"]
    web_enabled = config["interfaces"]["web"]["enabled"]
    
    print("🚀 启动混合模式...")
    print(f"CLI模式: {'✅' if cli_enabled else '❌'}")
    print(f"Web API模式: {'✅' if web_enabled else '❌'}")
    
    tasks = []
    
    # 启动Web API (如果启用)
    if web_enabled:
        def run_web_server():
            uvicorn.run(
                "web_main:app",
                host=web_config["host"],
                port=web_config["port"],
                log_level="info"
            )
        
        web_thread = threading.Thread(target=run_web_server, daemon=True)
        web_thread.start()
        print(f"🌐 Web API服务已启动: http://{web_config['host']}:{web_config['port']}")
    
    # 启动CLI (如果启用)
    if cli_enabled:
        print("💬 CLI模式已启动")
        await cli_main()
    else:
        # 如果只启用Web API，保持程序运行
        print("⏳ 仅Web API模式，按Ctrl+C退出")
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 再见！")

if __name__ == "__main__":
    asyncio.run(start_hybrid_mode())
```

## 🔧 改进建议和最佳实践

### 1. 统一配置管理
创建 `core/config_manager.py` 统一管理所有配置：

```python
# core/config_manager.py
"""
统一配置管理器 - 整合所有配置文件
"""

import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class AppConfig:
    """应用配置数据类"""
    name: str
    version: str
    mode: str

class ConfigManager:
    """统一配置管理器"""

    def __init__(self, config_dir: str = "config"):
        self.config_dir = config_dir
        self.app_config = self._load_app_config()
        self.llm_config = self._load_llm_config()
        self.mcp_config = self._load_mcp_config()
        self.persistence_config = self._load_persistence_config()

    def _load_app_config(self) -> Dict[str, Any]:
        """加载应用配置"""
        config_path = os.path.join(self.config_dir, "app_config.json")
        return self._load_json_config(config_path, self._get_default_app_config())

    def _load_llm_config(self) -> Dict[str, Any]:
        """加载LLM配置"""
        return self._load_json_config("llm_config.json", {})

    def _load_mcp_config(self) -> Dict[str, Any]:
        """加载MCP配置"""
        return self._load_json_config("mcp_config.json", {})

    def _load_persistence_config(self) -> Dict[str, Any]:
        """加载持久化配置"""
        return self._load_json_config("persistence_config.json", {})

    def _load_json_config(self, path: str, default: Dict[str, Any]) -> Dict[str, Any]:
        """通用JSON配置加载器"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"⚠️ 配置文件 {path} 不存在，使用默认配置")
            return default
        except Exception as e:
            print(f"❌ 加载配置文件 {path} 失败: {e}，使用默认配置")
            return default

    def _get_default_app_config(self) -> Dict[str, Any]:
        """获取默认应用配置"""
        return {
            "app": {"name": "LangGraph智能体系统", "version": "2.0.0", "mode": "hybrid"},
            "interfaces": {
                "cli": {"enabled": True, "default_user": "cli_user"},
                "web": {"enabled": False, "host": "0.0.0.0", "port": 8000}
            },
            "features": {"real_time_streaming": True, "session_isolation": True}
        }

    def is_cli_enabled(self) -> bool:
        """检查CLI是否启用"""
        return self.app_config.get("interfaces", {}).get("cli", {}).get("enabled", True)

    def is_web_enabled(self) -> bool:
        """检查Web API是否启用"""
        return self.app_config.get("interfaces", {}).get("web", {}).get("enabled", False)

    def get_web_config(self) -> Dict[str, Any]:
        """获取Web配置"""
        return self.app_config.get("interfaces", {}).get("web", {})
```

### 2. 增强错误处理
创建 `core/error_handler.py`：

```python
# core/error_handler.py
"""
统一错误处理机制
"""

import logging
import traceback
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ErrorHandler:
    """统一错误处理器"""

    @staticmethod
    async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
        """全局异常处理器"""
        error_id = id(exc)
        logger.error(f"全局异常 [{error_id}]: {str(exc)}")
        logger.error(f"异常详情 [{error_id}]: {traceback.format_exc()}")

        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": "Internal server error",
                "error_id": error_id,
                "detail": str(exc) if request.app.debug else "服务器内部错误"
            }
        )

    @staticmethod
    async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
        """HTTP异常处理器"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": exc.detail,
                "status_code": exc.status_code
            }
        )

    @staticmethod
    def handle_websocket_error(error: Exception, session_id: str) -> Dict[str, Any]:
        """WebSocket错误处理"""
        error_id = id(error)
        logger.error(f"WebSocket错误 [{error_id}] 会话 {session_id}: {str(error)}")

        return {
            "type": "error",
            "error": str(error),
            "error_id": error_id,
            "session_id": session_id
        }
```

## 🧪 测试验证

### 测试步骤

1. **测试现有CLI功能**：
```bash
# 确保原有功能正常
uv run python main.py

# 测试重构后的CLI
uv run python main_v2.py
```

2. **测试Web API**：
```bash
# 启动Web API
uv run python web_main.py

# 在另一个终端测试API
curl http://localhost:8000/health
```

3. **测试混合模式**：
```bash
# 启动混合模式
uv run python hybrid_main.py
```

### 单元测试
创建 `tests/test_core.py`：

```python
# tests/test_core.py
"""
核心功能单元测试
"""

import pytest
import asyncio
from core.agent_core import AgentCore
from core.config_manager import ConfigManager

@pytest.mark.asyncio
async def test_agent_core_initialization():
    """测试智能体核心初始化"""
    config_manager = ConfigManager()
    agent_core = AgentCore(config_manager)

    await agent_core.initialize()
    assert agent_core._initialized is True
    assert agent_core.app is not None

@pytest.mark.asyncio
async def test_message_processing():
    """测试消息处理"""
    config_manager = ConfigManager()
    agent_core = AgentCore(config_manager)
    await agent_core.initialize()

    session_config = {"configurable": {"thread_id": "test_session"}}

    results = []
    async for result in agent_core.process_message("你好", session_config):
        results.append(result)

    assert len(results) > 0
    assert results[-1]["type"] in ["stream_chunk", "final_result"]

def test_config_manager():
    """测试配置管理器"""
    config_manager = ConfigManager()

    assert config_manager.app_config is not None
    assert isinstance(config_manager.is_cli_enabled(), bool)
    assert isinstance(config_manager.is_web_enabled(), bool)
```

### API测试
创建 `tests/test_api.py`：

```python
# tests/test_api.py
"""
API接口测试
"""

import pytest
from httpx import AsyncClient
from web_main import app

@pytest.mark.asyncio
async def test_health_check():
    """测试健康检查接口"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.get("/health")

    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

@pytest.mark.asyncio
async def test_create_session():
    """测试创建会话接口"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        response = await ac.post("/api/sessions", json={
            "title": "测试会话",
            "mode": "single_agent",
            "user_id": "test_user"
        })

    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "session" in data["data"]
```

### 验收标准

- [ ] 原有CLI功能完全正常
- [ ] Web API基础功能可用
- [ ] WebSocket连接正常
- [ ] 混合模式可以同时运行
- [ ] 配置文件控制功能启用/禁用
- [ ] 数据库和会话共享正常
- [ ] 单元测试通过率 > 90%
- [ ] API测试覆盖所有接口
- [ ] 错误处理机制正常工作
- [ ] 日志记录完整

## 📝 注意事项

1. **保持向后兼容**：原有的`main.py`保持不变，可以随时回退
2. **渐进式启用**：通过配置文件逐步启用新功能
3. **共享数据**：CLI和Web API使用相同的数据库和会话
4. **错误处理**：确保Web API的错误不影响CLI功能

这个实施方案确保了零风险的升级路径，您可以随时在新旧版本之间切换。
