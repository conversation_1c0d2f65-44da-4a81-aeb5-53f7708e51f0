@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-delay: -1ms !important;
    animation-duration: 1ms !important;
    animation-iteration-count: 1 !important;
    background-attachment: initial !important;
    scroll-behavior: auto !important;
    transition-duration: 0s !important;
    transition-delay: 0s !important;
  }
}

/* 添加View Transitions支持 */
:root {
  --view-transition-duration: 0.5s;
  --transition-position-x: calc(100vw - 101px);
  --transition-position-y: 24px;
  --max-radius: 141vmax;
  --transition-easing: cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes circle-in {
  from {
    clip-path: circle(0% at var(--transition-position-x) var(--transition-position-y));
  }
  to {
    clip-path: circle(var(--max-radius) at var(--transition-position-x) var(--transition-position-y));
  }
}

::view-transition-group(root) {
  animation: none;
}

::view-transition-old(root) {
  animation: none;
  opacity: 1;
}

::view-transition-new(root) {
  animation: circle-in var(--view-transition-duration) var(--transition-easing) forwards;
  opacity: 1;
}

html.dark {
  color-scheme: dark;
  transition: background-color var(--view-transition-duration) ease;
}

/* 修复iOS Safari支持 */
@supports not (animation-timeline: view-timeline-inset(0%)) {
  @media (prefers-reduced-motion: no-preference) {
    ::view-transition-group(*),
    ::view-transition-old(*),
    ::view-transition-new(*) {
      animation-duration: 0s;
    }
  }
}

*,
::before,
::after {
  box-sizing: border-box;
}

html {
  line-height: 1.4;
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
}

html.dark {
  color-scheme: dark;
}

body {
  margin: 0;
  width: 100%;
  min-width: 320px;
  min-height: 100vh;
  line-height: 24px;
  font-family: var(--vp-font-family-base);
  font-size: 16px;
  font-weight: 400;
  color: var(--vp-c-text-1);
  font-synthesis: style;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--view-transition-duration) ease, color var(--view-transition-duration) ease;
}

main {
  display: block;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  line-height: 24px;
  font-size: 16px;
  font-weight: 400;
}

p {
  margin: 0;
}

strong,
b {
  font-weight: 600;
}

/**
 * Avoid 300ms click delay on touch devices that support the `touch-action`
 * CSS property.
 *
 * In particular, unlike most other browsers, IE11+Edge on Windows 10 on
 * touch devices and IE Mobile 10-11 DON'T remove the click delay when
 * `<meta name="viewport" content="width=device-width">` is present.
 * However, they DO support removing the click delay via
 * `touch-action: manipulation`.
 *
 * See:
 * - http://v4-alpha.getbootstrap.com/content/reboot/#click-delay-optimization-for-touch
 * - http://caniuse.com/#feat=css-touch-action
 * - http://patrickhlauke.github.io/touch/tests/results/#suppressing-300ms-delay
 */
a,
area,
button,
[role='button'],
input,
label,
select,
summary,
textarea {
  touch-action: manipulation;
}

a {
  color: inherit;
  text-decoration: inherit;
}

ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

blockquote {
  margin: 0;
}

body pre,
body code,
body kbd,
body samp {
  font-family: var(--vp-font-family-mono);
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
}

figure {
  margin: 0;
}

img,
video {
  max-width: 100%;
  height: auto;
}

button,
input,
optgroup,
select,
textarea {
  border: 0;
  padding: 0;
  line-height: inherit;
  color: inherit;
}

button {
  padding: 0;
  font-family: inherit;
  background-color: transparent;
  background-image: none;
}

button:enabled,
[role='button']:enabled {
  cursor: pointer;
}

button:focus,
button:focus-visible {
  outline: 1px dotted;
  outline: 4px auto -webkit-focus-ring-color;
}

button:focus:not(:focus-visible) {
  outline: none !important;
}

input:focus,
textarea:focus,
select:focus {
  outline: none;
}

table {
  border-collapse: collapse;
}

input {
  background-color: transparent;
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: var(--vp-c-text-3);
}

input::-ms-input-placeholder,
textarea::-ms-input-placeholder {
  color: var(--vp-c-text-3);
}

input::placeholder,
textarea::placeholder {
  color: var(--vp-c-text-3);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
}

textarea {
  resize: vertical;
}

select {
  -webkit-appearance: none;
}

fieldset {
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
li,
p {
  overflow-wrap: break-word;
}

vite-error-overlay {
  z-index: 9999;
}

mjx-container {
  overflow-x: auto;
}

mjx-container > svg {
  display: inline-block;
  margin: auto;
}
