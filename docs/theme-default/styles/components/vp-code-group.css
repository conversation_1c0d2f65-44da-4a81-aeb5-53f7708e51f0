.vp-code-group {
  margin-top: 16px;
}

.vp-code-group .tabs {
  position: relative;
  display: flex;
  margin-right: -24px;
  margin-left: -24px;
  padding: 0 12px;
  background-color: var(--vp-code-tab-bg);
  overflow-x: auto;
  overflow-y: hidden;
  box-shadow: inset 0 -1px var(--vp-code-tab-divider);
}

@media (min-width: 640px) {
  .vp-code-group .tabs {
    margin-right: 0;
    margin-left: 0;
    border-radius: 8px 8px 0 0;
  }
}

.vp-code-group .tabs input {
  position: fixed;
  opacity: 0;
  pointer-events: none;
}

.vp-code-group .tabs label {
  position: relative;
  display: inline-block;
  border-bottom: 1px solid transparent;
  padding: 0 12px;
  line-height: 48px;
  font-size: 14px;
  font-weight: 500;
  color: var(--vp-code-tab-text-color);
  white-space: nowrap;
  cursor: pointer;
  transition: color 0.25s;
}

.vp-code-group .tabs label::after {
  position: absolute;
  right: 8px;
  bottom: -1px;
  left: 8px;
  z-index: 1;
  height: 2px;
  border-radius: 2px;
  content: '';
  background-color: transparent;
  transition: background-color 0.25s;
}

.vp-code-group label:hover {
  color: var(--vp-code-tab-hover-text-color);
}

.vp-code-group input:checked + label {
  color: var(--vp-code-tab-active-text-color);
}

.vp-code-group input:checked + label::after {
  background-color: var(--vp-code-tab-active-bar-color);
}

.vp-code-group div[class*='language-'],
.vp-block {
  display: none;
  margin-top: 0 !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.vp-code-group div[class*='language-'].active,
.vp-block.active {
  display: block;
}

.vp-block {
  padding: 20px 24px;
}
