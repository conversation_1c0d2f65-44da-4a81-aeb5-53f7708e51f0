# 国际化

MateChat组件默认使用中文，同时支持其他语言的切换。

### 配置

在`main.js`中引入McI18n，使用`McI18n.use`方法初始化默认语言, 如果不进行初始化，MateChat将默认使用中文。

```ts
import { createApp } from 'vue';
import App from './App.vue';
import MateChat from '@matechat/core';
import McI18n from '@matechat/core/Locale';

// 初始化默认语言
McI18n.use('zh-cn');

createApp(App).use(MateChat).mount('#app');
```


### 切换语言

通过使用`McI18n.use`方法切换当前使用的语言。

```vue
<script setup>
  // 导入McI18n实例
  import McI18n from '@matechat/core/Locale';

  // 使用use方法切换语言，并实时生效
  McI18n.use(locale);

</script>

```

### 修改默认文案

通过`McI18n.mergeLocaleMessages`方法实现默认语言的覆盖和修改。

如将`Input`组件中的`停止回答`中文文案，修改成`停止`，可进行如下配置：

```ts
import McI18n from '@matechat/core/Locale';

// 覆盖默认文案，可按需覆盖，未配置字段将会保持原状
McI18n.mergeLocaleMessages('zh-cn', {
    Input: {
        pauseAnswer: '停止',
    }
})


```

### 自定义添加语言

通过`McI18n.mergeLocaleMessages`方法实现MateChat语言的扩展。

如新增一种语言`customLocale`， `customMessages`配置请参考 [components/Locale](https://gitcode.com/DevCloudFE/MateChat/tree/main/components/Locale)

```ts
// 新增一种语言配置，
const customMessages = {
    Input: {
        send: 'customSend', // 更多翻译字段请参考components/Locale补充完整
    }
}

McI18n.mergeLocaleMessages('customLocale', customMessages)
```

## 已支持语言

- 简体中文(zh-cn)
- 英语(en-us)


如果你需要使用其他的语言，可通过自定义添加语言进行配置，也欢迎 [贡献](https://gitcode.com/DevCloudFE/MateChat/blob/main/CONTRIBUTING.md)。