# MateChat-dev 与 my_project 详细集成方案

## 📋 项目概述

本文档详细描述了如何将 MateChat-dev 前端UI库与 my_project 后端API进行集成，构建一个现代化的AI聊天应用。

### 技术栈组合
- **后端**: my_project (FastAPI + LangGraph + WebSocket)
- **前端**: MateChat-dev (Vue3 + TypeScript + AI聊天组件)
- **通信**: REST API + WebSocket 实时通信

## 🎯 集成目标

1. **前后端分离**: 利用my_project的Web API架构
2. **现代化UI**: 使用MateChat的专业AI聊天组件
3. **实时通信**: WebSocket支持流式响应
4. **生产就绪**: 完整的错误处理和状态管理

## 🏗️ 架构设计

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    MateChat 前端层                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  McBubble   │  │  McLayout   │  │  McHeader   │         │
│  │   组件      │  │    组件     │  │    组件     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    通信层                                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ REST API    │  │ WebSocket   │  │ 状态管理    │         │
│  │   调用      │  │   实时通信  │  │   (Pinia)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                  my_project 后端层                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ FastAPI     │  │ LangGraph   │  │ MCP Tools   │         │
│  │   Web API   │  │   Agent     │  │   35+ 工具  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 📦 第一阶段：环境准备

### 1.1 后端环境配置

```bash
# 进入my_project目录
cd /Users/<USER>/Desktop/my_project

# 安装依赖
uv sync

# 启动后端服务
uv run start_web.py
```

**验证后端服务**:
```bash
# 健康检查
curl http://localhost:8000/health

# API文档
open http://localhost:8000/docs
```

### 1.2 前端项目创建

```bash
# 创建前端项目
cd /Users/<USER>/Desktop
npm create vite@latest matechat-frontend -- --template vue-ts
cd matechat-frontend

# 安装MateChat依赖
npm install vue-devui @matechat/core @devui-design/icons

# 安装其他必要依赖
npm install axios pinia @vueuse/core
```

## 🔧 第二阶段：核心集成

### 2.1 API服务封装

创建 `src/services/api.ts`:

```typescript
import axios from 'axios';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp?: string;
}

export interface ChatRequest {
  message: string;
  thread_id?: string;
  stream?: boolean;
}

export interface ChatResponse {
  message: string;
  thread_id: string;
  timestamp: string;
}

export class ChatAPI {
  private baseURL = 'http://localhost:8000';
  private client = axios.create({
    baseURL: this.baseURL,
    timeout: 30000,
  });

  // 发送聊天消息
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const response = await this.client.post('/api/chat', request);
    return response.data;
  }

  // 流式聊天
  async sendStreamMessage(request: ChatRequest): Promise<ReadableStream> {
    const response = await fetch(`${this.baseURL}/api/chat/stream`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    
    if (!response.body) {
      throw new Error('No response body');
    }
    
    return response.body;
  }

  // WebSocket连接
  connectWebSocket(threadId: string): WebSocket {
    return new WebSocket(`ws://localhost:8000/ws/${threadId}`);
  }

  // 获取会话历史
  async getSessionHistory(threadId: string): Promise<ChatMessage[]> {
    const response = await this.client.get(`/api/sessions/${threadId}/history`);
    return response.data;
  }

  // 获取可用工具
  async getAvailableTools(): Promise<any[]> {
    const response = await this.client.get('/api/tools');
    return response.data;
  }
}

export const chatAPI = new ChatAPI();
```

### 2.2 状态管理 (Pinia Store)

创建 `src/stores/chat.ts`:

```typescript
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { chatAPI, type ChatMessage } from '@/services/api';

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref<ChatMessage[]>([]);
  const currentThreadId = ref<string>('');
  const isLoading = ref(false);
  const isConnected = ref(false);
  const websocket = ref<WebSocket | null>(null);

  // 计算属性
  const hasMessages = computed(() => messages.value.length > 0);
  const lastMessage = computed(() => 
    messages.value[messages.value.length - 1]
  );

  // 方法
  const generateThreadId = () => {
    return `thread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const addMessage = (message: ChatMessage) => {
    messages.value.push({
      ...message,
      timestamp: message.timestamp || new Date().toISOString(),
    });
  };

  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    // 确保有线程ID
    if (!currentThreadId.value) {
      currentThreadId.value = generateThreadId();
    }

    // 添加用户消息
    addMessage({
      role: 'user',
      content: content.trim(),
    });

    isLoading.value = true;

    try {
      // 发送到后端
      const response = await chatAPI.sendMessage({
        message: content.trim(),
        thread_id: currentThreadId.value,
      });

      // 添加AI回复
      addMessage({
        role: 'assistant',
        content: response.message,
      });

    } catch (error) {
      console.error('发送消息失败:', error);
      addMessage({
        role: 'assistant',
        content: '抱歉，发送消息时出现错误，请重试。',
      });
    } finally {
      isLoading.value = false;
    }
  };

  const connectWebSocket = () => {
    if (!currentThreadId.value) {
      currentThreadId.value = generateThreadId();
    }

    try {
      websocket.value = chatAPI.connectWebSocket(currentThreadId.value);
      
      websocket.value.onopen = () => {
        isConnected.value = true;
        console.log('WebSocket连接已建立');
      };

      websocket.value.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'message') {
          addMessage({
            role: 'assistant',
            content: data.content,
          });
        }
      };

      websocket.value.onclose = () => {
        isConnected.value = false;
        console.log('WebSocket连接已关闭');
      };

      websocket.value.onerror = (error) => {
        console.error('WebSocket错误:', error);
        isConnected.value = false;
      };

    } catch (error) {
      console.error('WebSocket连接失败:', error);
    }
  };

  const disconnectWebSocket = () => {
    if (websocket.value) {
      websocket.value.close();
      websocket.value = null;
      isConnected.value = false;
    }
  };

  const clearMessages = () => {
    messages.value = [];
    currentThreadId.value = '';
  };

  const loadSessionHistory = async (threadId: string) => {
    try {
      const history = await chatAPI.getSessionHistory(threadId);
      messages.value = history;
      currentThreadId.value = threadId;
    } catch (error) {
      console.error('加载会话历史失败:', error);
    }
  };

  return {
    // 状态
    messages,
    currentThreadId,
    isLoading,
    isConnected,
    
    // 计算属性
    hasMessages,
    lastMessage,
    
    // 方法
    addMessage,
    sendMessage,
    connectWebSocket,
    disconnectWebSocket,
    clearMessages,
    loadSessionHistory,
  };
});
```

### 2.3 主聊天组件

创建 `src/components/ChatInterface.vue`:

```vue
<template>
  <McLayout class="chat-container">
    <!-- 头部 -->
    <McHeader 
      :title="'AI智能助手'" 
      :logoImg="'https://matechat.gitcode.com/logo.svg'"
    >
      <template #operationArea>
        <div class="operations">
          <button @click="clearChat" class="clear-btn">
            <i class="icon-delete"></i>
            清空对话
          </button>
          <div class="connection-status" :class="{ connected: isConnected }">
            {{ isConnected ? '已连接' : '未连接' }}
          </div>
        </div>
      </template>
    </McHeader>

    <!-- 欢迎页面 -->
    <McLayoutContent
      v-if="!hasMessages"
      class="welcome-container"
    >
      <McIntroduction
        :logoImg="'https://matechat.gitcode.com/logo2x.svg'"
        :title="'AI智能助手'"
        :subTitle="'Hi，欢迎使用智能助手'"
        :description="welcomeDescription"
      />
      <McPrompt
        :list="quickPrompts"
        :direction="'horizontal'"
        class="quick-prompts"
        @itemClick="handleQuickPrompt"
      />
    </McLayoutContent>

    <!-- 聊天内容 -->
    <McLayoutContent v-else class="messages-container">
      <div class="messages-list" ref="messagesContainer">
        <template v-for="(message, index) in messages" :key="index">
          <McBubble
            :content="message.content"
            :align="message.role === 'user' ? 'right' : 'left'"
            :avatarConfig="getAvatarConfig(message.role)"
            :timestamp="formatTimestamp(message.timestamp)"
          />
        </template>
        
        <!-- 加载指示器 -->
        <McBubble
          v-if="isLoading"
          content="正在思考中..."
          align="left"
          :avatarConfig="getAvatarConfig('assistant')"
          class="loading-message"
        />
      </div>
    </McLayoutContent>

    <!-- 输入区域 -->
    <div class="input-container">
      <McInput
        v-model="inputMessage"
        :placeholder="'输入您的问题...'"
        :disabled="isLoading"
        @keyup.enter="handleSendMessage"
        class="message-input"
      >
        <template #suffix>
          <button 
            @click="handleSendMessage"
            :disabled="!inputMessage.trim() || isLoading"
            class="send-button"
          >
            <i class="icon-send"></i>
          </button>
        </template>
      </McInput>
    </div>
  </McLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useChatStore } from '@/stores/chat';
import {
  McLayout,
  McHeader,
  McLayoutContent,
  McIntroduction,
  McPrompt,
  McBubble,
  McInput,
} from '@matechat/core';

const chatStore = useChatStore();
const inputMessage = ref('');
const messagesContainer = ref<HTMLElement>();

// 计算属性
const messages = computed(() => chatStore.messages);
const hasMessages = computed(() => chatStore.hasMessages);
const isLoading = computed(() => chatStore.isLoading);
const isConnected = computed(() => chatStore.isConnected);

// 欢迎信息
const welcomeDescription = ref([
  '我是您的AI智能助手，可以帮助您：',
  '• 回答各种问题',
  '• 搜索网络信息',
  '• 生成图表和可视化',
  '• 进行逻辑推理和分析',
]);

// 快捷提示
const quickPrompts = ref([
  { label: '你好，请介绍一下自己' },
  { label: '帮我搜索最新的AI发展趋势' },
  { label: '生成一个数据可视化图表' },
  { label: '帮我分析一个复杂问题' },
]);

// 方法
const getAvatarConfig = (role: 'user' | 'assistant') => {
  if (role === 'user') {
    return {
      imgSrc: 'https://matechat.gitcode.com/png/demo/userAvatar.svg'
    };
  } else {
    return {
      imgSrc: 'https://matechat.gitcode.com/logo.svg'
    };
  }
};

const formatTimestamp = (timestamp?: string) => {
  if (!timestamp) return '';
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

const handleSendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return;
  
  const message = inputMessage.value;
  inputMessage.value = '';
  
  await chatStore.sendMessage(message);
  await scrollToBottom();
};

const handleQuickPrompt = (prompt: { label: string }) => {
  inputMessage.value = prompt.label;
  handleSendMessage();
};

const clearChat = () => {
  chatStore.clearMessages();
};

const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 监听消息变化，自动滚动到底部
watch(messages, () => {
  scrollToBottom();
}, { deep: true });

// 生命周期
onMounted(() => {
  chatStore.connectWebSocket();
});

onUnmounted(() => {
  chatStore.disconnectWebSocket();
});
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.operations {
  display: flex;
  align-items: center;
  gap: 12px;
}

.clear-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-btn:hover {
  background: #f5f5f5;
}

.connection-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background: #f0f0f0;
  color: #666;
}

.connection-status.connected {
  background: #e6f7ff;
  color: #1890ff;
}

.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 24px;
  padding: 40px;
}

.quick-prompts {
  max-width: 600px;
}

.messages-container {
  flex: 1;
  overflow: hidden;
}

.messages-list {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.loading-message {
  opacity: 0.7;
}

.input-container {
  padding: 16px;
  border-top: 1px solid #eee;
  background: white;
}

.message-input {
  width: 100%;
}

.send-button {
  padding: 8px;
  border: none;
  background: #1890ff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.send-button:not(:disabled):hover {
  background: #40a9ff;
}
</style>
```

## 🚀 第三阶段：应用配置

### 3.1 主应用配置

修改 `src/main.ts`:

```typescript
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';

// MateChat 组件库
import MateChat from '@matechat/core';
import '@devui-design/icons/icomoon/devui-icon.css';

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(MateChat);

app.mount('#app');
```

### 3.2 根组件

修改 `src/App.vue`:

```vue
<template>
  <div id="app">
    <ChatInterface />
  </div>
</template>

<script setup lang="ts">
import ChatInterface from '@/components/ChatInterface.vue';
</script>

<style>
#app {
  height: 100vh;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}
</style>
```

## 📋 第四阶段：测试验证

### 4.1 启动测试

```bash
# 启动后端 (终端1)
cd /Users/<USER>/Desktop/my_project
uv run start_web.py

# 启动前端 (终端2)
cd /Users/<USER>/Desktop/matechat-frontend
npm run dev
```

### 4.2 功能测试清单

- [ ] 基础聊天功能
- [ ] WebSocket实时通信
- [ ] 会话历史加载
- [ ] 错误处理
- [ ] 响应式布局
- [ ] 快捷提示功能

## 🎯 第五阶段：优化增强

### 5.1 流式响应支持
### 5.2 多会话管理
### 5.3 文件上传功能
### 5.4 主题切换
### 5.5 国际化支持

## 📚 参考资源

- [MateChat 官方文档](https://matechat.gitcode.com)
- [my_project API 文档](http://localhost:8000/docs)
- [Vue3 官方文档](https://vuejs.org/)
- [Pinia 状态管理](https://pinia.vuejs.org/)

---

**下一步**: 请参考 `LangGraph_Optimization_Migration_Plan.md` 了解如何将 LangGraphAgentv3.2 的优化功能合并到此架构中。
