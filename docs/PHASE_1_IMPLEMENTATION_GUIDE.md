# 第一阶段：技术架构优化实施指南

## 🎯 阶段目标

在1-2周内完成技术架构优化，解决当前技术债务，建立生产级基础设施。

## 📋 任务清单

### Day 1-3: Docker容器化 ⭐⭐⭐⭐⭐

#### 任务1.1: 创建Dockerfile
```dockerfile
# backend.Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装uv
RUN pip install uv

# 复制依赖文件
COPY pyproject.toml ./
COPY uv.lock ./

# 安装Python依赖
RUN uv sync --frozen

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

CMD ["uv", "run", "start_web.py"]
```

```dockerfile
# frontend.Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY frontend/package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY frontend/ .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### 任务1.2: 创建docker-compose.yml
```yaml
version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: backend.Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///data/agent_memory.db
      - CORS_ORIGINS=http://localhost:3000,http://localhost:80
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - redis
    networks:
      - app-network

  frontend:
    build:
      context: .
      dockerfile: frontend.Dockerfile
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  redis_data:

networks:
  app-network:
    driver: bridge
```

#### 任务1.3: 环境配置
创建 `.env` 文件：
```bash
# 应用配置
APP_ENV=development
DEBUG=true
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_URL=sqlite:///data/agent_memory.db

# Redis配置
REDIS_URL=redis://redis:6379/0

# AI模型配置
ZHIPU_API_KEY=your-zhipu-api-key
MODEL_NAME=GLM-4-Flash

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:80
```

### Day 4-5: CI/CD流水线 ⭐⭐⭐⭐

#### 任务2.1: GitHub Actions配置
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install uv
      run: pip install uv
    
    - name: Install dependencies
      run: uv sync
    
    - name: Run backend tests
      run: uv run pytest tests/ -v --cov=./ --cov-report=xml
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run frontend tests
      run: |
        cd frontend
        npm run test:unit
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Build and push Docker images
      run: |
        docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ github.sha }} -f backend.Dockerfile .
        docker build -t ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ github.sha }} -f frontend.Dockerfile .
        docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ github.sha }}
        docker push ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ github.sha }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # 这里添加部署脚本
```

### Day 6-8: 测试完善 ⭐⭐⭐⭐

#### 任务3.1: 前端测试框架
```bash
# 安装测试依赖
cd frontend
npm install --save-dev @vue/test-utils jest @types/jest ts-jest vue-jest
```

```javascript
// frontend/jest.config.js
module.exports = {
  preset: '@vue/cli-plugin-unit-jest/presets/typescript-and-babel',
  testEnvironment: 'jsdom',
  transform: {
    '^.+\\.vue$': 'vue-jest',
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,vue}',
    '!src/main.ts',
    '!src/**/*.d.ts',
  ],
  coverageReporters: ['text', 'lcov', 'html'],
};
```

#### 任务3.2: 组件测试示例
```typescript
// frontend/tests/unit/ChatInterface.spec.ts
import { mount } from '@vue/test-utils';
import { createPinia } from 'pinia';
import ChatInterface from '@/components/ChatInterface.vue';

describe('ChatInterface.vue', () => {
  let wrapper: any;
  
  beforeEach(() => {
    const pinia = createPinia();
    wrapper = mount(ChatInterface, {
      global: {
        plugins: [pinia],
      },
    });
  });

  it('renders welcome message when no messages', () => {
    expect(wrapper.find('.welcome-container').exists()).toBe(true);
    expect(wrapper.text()).toContain('AI智能助手');
  });

  it('sends message when enter is pressed', async () => {
    const input = wrapper.find('.message-input');
    await input.setValue('Hello, AI!');
    await input.trigger('keyup.enter');
    
    // 验证消息发送逻辑
    expect(wrapper.vm.inputMessage).toBe('');
  });
});
```

#### 任务3.3: E2E测试配置
```typescript
// tests/e2e/chat.spec.ts
import { test, expect } from '@playwright/test';

test('basic chat functionality', async ({ page }) => {
  await page.goto('http://localhost:3000');
  
  // 等待页面加载
  await expect(page.locator('.welcome-container')).toBeVisible();
  
  // 发送消息
  await page.fill('.message-input', 'Hello, AI assistant!');
  await page.press('.message-input', 'Enter');
  
  // 验证消息显示
  await expect(page.locator('.user-message')).toContainText('Hello, AI assistant!');
  
  // 等待AI回复
  await expect(page.locator('.ai-message')).toBeVisible({ timeout: 10000 });
});
```

### Day 9-10: 性能监控 ⭐⭐⭐⭐

#### 任务4.1: Prometheus集成
```python
# utils/metrics.py
from prometheus_client import Counter, Histogram, Gauge, generate_latest
import time

# 定义指标
REQUEST_COUNT = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('http_request_duration_seconds', 'HTTP request duration')
ACTIVE_CONNECTIONS = Gauge('websocket_connections_active', 'Active WebSocket connections')

class MetricsMiddleware:
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "http":
            start_time = time.time()
            
            # 处理请求
            await self.app(scope, receive, send)
            
            # 记录指标
            duration = time.time() - start_time
            REQUEST_DURATION.observe(duration)
            REQUEST_COUNT.labels(
                method=scope["method"],
                endpoint=scope["path"]
            ).inc()
```

#### 任务4.2: Grafana仪表板配置
```json
{
  "dashboard": {
    "title": "my_project Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

## 🚀 部署和验证

### 部署步骤
```bash
# 1. 构建镜像
docker-compose build

# 2. 启动服务
docker-compose up -d

# 3. 验证服务
curl http://localhost:8000/health
curl http://localhost:80

# 4. 查看日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 验证清单
- [ ] Docker容器正常启动
- [ ] 前后端服务通信正常
- [ ] 数据持久化正常
- [ ] Redis缓存连接正常
- [ ] 监控指标收集正常
- [ ] CI/CD流水线运行正常
- [ ] 测试覆盖率>80%

## 📊 成功标准

### 技术指标
- ✅ 容器化部署成功率100%
- ✅ 自动化测试覆盖率>80%
- ✅ CI/CD流水线构建时间<10分钟
- ✅ 监控指标收集延迟<1秒

### 性能指标
- ✅ 应用启动时间<30秒
- ✅ API响应时间<2秒
- ✅ 前端首屏加载时间<3秒
- ✅ 内存使用<512MB

### 稳定性指标
- ✅ 容器重启成功率100%
- ✅ 数据一致性验证通过
- ✅ 错误监控和告警正常
- ✅ 日志收集和分析正常

---

**预计完成时间**: 10个工作日  
**关键里程碑**: Docker化完成、CI/CD上线、监控系统运行  
**下一阶段**: 用户体验增强 (流式响应优化)
