要查看最近执行的流水线列表，您可以按照以下步骤操作：

1. **访问流水线首页**：

   - 登录到您的云服务平台，例如华为云的CodeArts Pipeline首页。

2. **查看流水线列表**：

   - 在流水线列表页，您可以展示当前用户下的所有流水线。列表中会显示流水线的名称、最近一次执行信息、工作流状态、开始时间和执行时长等。

3. **过滤和搜索**：

   - 您可以使用下拉框根据“全部流水线”、“我新建的”及“最后一次记录由我执行的”进行过滤。
   - 支持在搜索栏中输入流水线名称关键字进行搜索。

4. **查看执行历史**：

   - 单击流水线名称，进入流水线的“执行历史”页面，展示流水线的执行记录。您可以使用时间筛选器选择时间段进行过滤，支持查看最近90天的执行记录，默认展示最近31天的执行记录。

5. **查看流水线详情**：

   - 单击执行编号，进入“流水线详情”页面，查看对应执行记录的详情。

6. **使用API查询**：

   - 如果您需要通过API获取流水线最近一次运行状态，可以使用如下接口：`GET /v2/pipelines/{pipeline_id}/status`，其中`pipeline_id`是流水线ID。这个接口会返回流水线的执行状态、执行结果、执行人、启动时间和结束时间等信息。

7. **获取流水线列表和执行状况**：
   - 您还可以使用POST请求`/v5/{project_id}/api/pipelines/list`来获取流水线列表和项目下流水线的执行状况。

通过上述步骤，您可以有效地查看和管理最近执行的流水线列表及其详细信息。
