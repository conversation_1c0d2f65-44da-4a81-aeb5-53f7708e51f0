# LangGraph Agent 混合架构系统 - 详细架构说明书

## 📋 目录
1. [项目概述](#项目概述)
2. [架构模式](#架构模式)
3. [项目结构](#项目结构)
4. [系统流程图](#系统流程图)
5. [文件功能说明](#文件功能说明)
6. [功能介绍](#功能介绍)
7. [技术栈](#技术栈)
8. [部署指南](#部署指南)

## 项目概述

### 基本信息
- **项目名称**: LangGraph Agent 混合架构系统
- **版本**: 2.0.0
- **架构模式**: CLI+Web API混合模式
- **核心技术**: LangGraph + FastAPI + WebSocket
- **包管理**: 纯uv管理
- **Python版本**: 3.11+

### 项目特点
- 🧠 **智能对话**: 基于LangGraph的多轮对话系统
- 🛠️ **工具调用**: 支持35+个MCP工具
- 💾 **持久化**: SQLite数据库存储对话历史
- 🔄 **会话管理**: 多用户多会话支持
- 🌐 **混合架构**: CLI和Web API双模式
- 📡 **实时通信**: WebSocket支持
- 🔒 **生产就绪**: 完整的错误处理和配置管理

## 架构模式

### 四层混合架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (User Interface)                │
├─────────────────────┬───────────────────────────────────────┤
│    CLI Interface    │         Web API Interface             │
│    (main.py)        │      (interfaces/web_api.py)          │
│                     │    ┌─────────────┬─────────────────┐   │
│                     │    │ REST API    │   WebSocket     │   │
│                     │    │ Endpoints   │   Real-time     │   │
└─────────────────────┴────┴─────────────┴─────────────────┴───┘
┌─────────────────────────────────────────────────────────────┐
│                   服务抽象层 (Service Layer)                  │
├─────────────────────┬───────────────────────────────────────┤
│  Config Manager     │         Error Handler                 │
│ (services/config_   │      (services/error_handler.py)      │
│  manager.py)        │                                       │
└─────────────────────┴───────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   核心业务层 (Core Business)                  │
├─────────────────────────────────────────────────────────────┤
│                  Agent Core                                 │
│              (core/agent_core.py)                           │
│        ┌─────────────────┬─────────────────────┐            │
│        │   LangGraph     │    Session Manager  │            │
│        │   StateGraph    │    Multi-threading  │            │
│        │   ToolNode      │    Memory Management│            │
└────────┴─────────────────┴─────────────────────┴────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   数据存储层 (Data Storage)                   │
├─────────────────────┬───────────────────────────────────────┤
│   SQLite Database   │         Configuration Files           │
│ (data/agent_memory  │        (config/*.json)                │
│      .db)           │                                       │
│  ┌─────────────────┐│  ┌─────────────────────────────────┐  │
│  │ AsyncSqliteSaver││  │ LLM Config │ MCP Config │ etc. │  │
│  │ Checkpointer    ││  │            │            │      │  │
│  └─────────────────┘│  └─────────────────────────────────┘  │
└─────────────────────┴───────────────────────────────────────┘
```

### 工具层 (Utils Layer)
```
┌─────────────────────────────────────────────────────────────┐
│                     工具层 (Utils Layer)                     │
├─────────────────────┬───────────────────────────────────────┤
│    LLM Loader       │         MCP Loader                    │
│ (utils/llm_loader   │      (utils/mcp_loader.py)            │
│     .py)            │                                       │
│  ┌─────────────────┐│  ┌─────────────────────────────────┐  │
│  │ Multi-Provider  ││  │ 35+ Tools │ Dynamic Loading │   │  │
│  │ Support         ││  │           │ MCP Protocol    │   │  │
│  └─────────────────┘│  └─────────────────────────────────┘  │
└─────────────────────┴───────────────────────────────────────┘
```

## 项目结构

### 目录结构
```
my_project/
├── 📁 config/                    # 配置文件目录
│   ├── llm_config.json          # LLM提供商配置
│   ├── mcp_config.json          # MCP工具配置
│   ├── persistence_config.json  # 持久化配置
│   └── web_config.json          # Web服务配置
├── 📁 core/                     # 核心业务层
│   ├── __init__.py
│   ├── agent_core.py            # LangGraph核心逻辑
│   └── simple_agent_core.py     # 简化版核心（测试用）
├── 📁 interfaces/               # 用户界面层
│   ├── __init__.py
│   ├── web_api.py               # Web API接口
│   └── simple_web_api.py        # 简化版API（测试用）
├── 📁 services/                 # 服务抽象层
│   ├── __init__.py
│   ├── config_manager.py        # 统一配置管理
│   └── error_handler.py         # 错误处理服务
├── 📁 utils/                    # 工具层
│   ├── __init__.py
│   ├── llm_loader.py           # LLM加载器
│   └── mcp_loader.py           # MCP工具加载器
├── 📁 tests/                    # 测试框架
│   ├── __init__.py
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   ├── e2e/                    # 端到端测试
│   └── *.py                    # 各种测试文件
├── 📁 docs/                     # 文档目录
│   ├── 项目架构说明书.md        # 本文档
│   └── 其他技术文档/
├── 📁 data/                     # 数据存储目录
│   └── agent_memory.db         # SQLite数据库
├── 📄 main.py                   # CLI模式主程序
├── 📄 start_web.py             # Web API启动脚本
├── 📄 pyproject.toml           # uv包管理配置
└── 📄 README.md                # 项目说明
```

### 启动方式
```bash
# CLI模式启动
uv run main.py

# Web API模式启动
uv run start_web.py

# 开发模式启动（带热重载）
uv run uvicorn interfaces.web_api:app --reload
```

## 系统流程图

### 整体架构流程
```mermaid
graph TB
    subgraph "用户界面层"
        CLI[CLI Interface<br/>main.py]
        WebAPI[Web API Interface<br/>interfaces/web_api.py]
        WS[WebSocket<br/>Real-time Communication]
    end
    
    subgraph "服务抽象层"
        CM[Config Manager<br/>services/config_manager.py]
        EH[Error Handler<br/>services/error_handler.py]
    end
    
    subgraph "核心业务层"
        AC[Agent Core<br/>core/agent_core.py]
        LG[LangGraph<br/>StateGraph + ToolNode]
        SM[Session Manager<br/>Multi-threading Support]
    end
    
    subgraph "工具层"
        LL[LLM Loader<br/>utils/llm_loader.py]
        ML[MCP Loader<br/>utils/mcp_loader.py]
    end
    
    subgraph "数据存储层"
        DB[(SQLite Database<br/>data/agent_memory.db)]
        CONFIG[Configuration Files<br/>config/*.json]
    end
    
    CLI --> AC
    WebAPI --> AC
    WS --> AC
    
    AC --> CM
    AC --> EH
    AC --> LG
    AC --> SM
    
    AC --> LL
    AC --> ML
    
    LG --> DB
    SM --> DB
    CM --> CONFIG
    LL --> CONFIG
    ML --> CONFIG
```

### CLI模式交互流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant CLI as CLI Interface
    participant AC as Agent Core
    participant LG as LangGraph
    participant DB as SQLite DB
    participant Tools as MCP Tools

    User->>CLI: 启动程序 (uv run main.py)
    CLI->>AC: 初始化Agent
    AC->>DB: 加载持久化配置
    AC->>Tools: 加载MCP工具
    AC->>LG: 创建StateGraph
    CLI->>User: 显示欢迎信息

    loop 对话循环
        User->>CLI: 输入消息
        CLI->>AC: 处理消息
        AC->>LG: 执行StateGraph
        LG->>Tools: 调用工具(如需要)
        Tools->>LG: 返回工具结果
        LG->>DB: 保存对话状态
        LG->>AC: 返回响应
        AC->>CLI: 格式化响应
        CLI->>User: 显示响应
    end
```

### Web API模式交互流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant API as Web API
    participant AC as Agent Core
    participant LG as LangGraph
    participant DB as SQLite DB
    participant WS as WebSocket

    Client->>API: POST /api/chat
    API->>AC: 处理聊天请求
    AC->>LG: 执行StateGraph
    LG->>DB: 保存/读取状态
    LG->>AC: 返回响应
    AC->>API: 格式化JSON响应
    API->>Client: 返回聊天结果

    Note over Client,WS: WebSocket实时通信
    Client->>WS: 连接 ws://localhost:8000/ws/{thread_id}
    WS->>AC: 建立会话连接
    Client->>WS: 发送消息
    WS->>AC: 处理实时消息
    AC->>LG: 执行StateGraph
    LG->>WS: 流式返回结果
    WS->>Client: 实时推送响应
```

## 文件功能说明

### 核心文件详解

#### 1. 主程序文件

##### `main.py` - CLI模式主程序
- **功能**: CLI交互式界面的主入口
- **核心特性**:
  - 严格遵循LangGraph官方标准实现
  - AsyncSqliteSaver持久化存储
  - 多会话管理系统
  - 完整的命令行交互逻辑
- **主要函数**:
  - `initialize_agent()`: 初始化Agent和工具
  - `create_checkpointer()`: 创建检查点存储器
  - `SimpleSessionManager`: 会话管理类
  - `main()`: 主交互循环
- **启动方式**: `uv run main.py`

##### `start_web.py` - Web API启动脚本
- **功能**: Web API服务的专用启动器
- **核心特性**:
  - 优雅的服务启动和关闭
  - 详细的启动信息显示
  - 信号处理和错误恢复
  - uvicorn服务器配置
- **启动方式**: `uv run start_web.py`

#### 2. 核心业务层 (core/)

##### `core/agent_core.py` - LangGraph核心逻辑
- **功能**: 系统的核心智能体实现
- **核心特性**:
  - LangGraph StateGraph实现
  - ToolNode工具节点管理
  - 异步消息处理
  - 会话状态管理
- **主要类**:
  - `AgentCore`: 主要的智能体核心类
  - `AgentState`: 基于MessagesState的状态类
- **关键方法**:
  - `initialize()`: 初始化Agent和工具
  - `create_graph()`: 创建LangGraph状态图
  - `process_message()`: 处理用户消息

#### 3. 用户界面层 (interfaces/)

##### `interfaces/web_api.py` - Web API接口
- **功能**: FastAPI Web服务实现
- **核心特性**:
  - RESTful API端点
  - WebSocket实时通信
  - 自动API文档生成
  - CORS跨域支持
- **主要端点**:
  - `GET /health`: 健康检查
  - `POST /api/chat`: 聊天接口
  - `POST /api/chat/stream`: 流式聊天
  - `GET /api/tools`: 工具列表
  - `WS /ws/{thread_id}`: WebSocket连接
- **数据模型**:
  - `ChatRequest`: 聊天请求模型
  - `ChatResponse`: 聊天响应模型

#### 4. 服务抽象层 (services/)

##### `services/config_manager.py` - 配置管理服务
- **功能**: 统一的配置文件管理
- **核心特性**:
  - 多配置文件支持
  - 配置验证和默认值
  - 热重载配置
  - 环境变量支持
- **主要类**:
  - `ConfigManager`: 配置管理器主类
- **支持的配置**:
  - LLM配置 (llm_config.json)
  - MCP工具配置 (mcp_config.json)
  - 持久化配置 (persistence_config.json)
  - Web服务配置 (web_config.json)

##### `services/error_handler.py` - 错误处理服务
- **功能**: 全局错误处理和日志记录
- **核心特性**:
  - 自定义错误类型
  - 错误分类和编码
  - 详细的错误日志
  - 错误恢复机制
- **主要类**:
  - `ErrorHandler`: 错误处理器主类
  - `CustomError`: 自定义错误类
  - `ErrorCode`: 错误代码枚举

#### 5. 工具层 (utils/)

##### `utils/llm_loader.py` - LLM加载器
- **功能**: 多LLM提供商支持和动态加载
- **核心特性**:
  - 支持多种LLM提供商 (OpenAI, Anthropic, 本地模型等)
  - 配置文件驱动的模型选择
  - 错误处理和回退机制
  - 模型参数自定义
- **主要函数**:
  - `load_llm_from_config()`: 从配置加载LLM
  - `create_openai_llm()`: 创建OpenAI模型
  - `create_anthropic_llm()`: 创建Anthropic模型

##### `utils/mcp_loader.py` - MCP工具加载器
- **功能**: MCP (Model Context Protocol) 工具动态加载
- **核心特性**:
  - 35+个可用工具
  - 动态工具发现和加载
  - 工具配置管理
  - 错误隔离和恢复
- **主要函数**:
  - `load_mcp_tools_from_config()`: 从配置加载MCP工具
  - `create_mcp_client()`: 创建MCP客户端
  - `get_available_tools()`: 获取可用工具列表

#### 6. 配置文件 (config/)

##### `config/llm_config.json` - LLM配置
```json
{
  "provider": "openai",
  "model": "gpt-4",
  "api_key": "${OPENAI_API_KEY}",
  "base_url": "https://api.openai.com/v1",
  "temperature": 0.7,
  "max_tokens": 4000
}
```

##### `config/mcp_config.json` - MCP工具配置
```json
{
  "tools": [
    {
      "name": "web-search",
      "enabled": true,
      "config": {...}
    },
    {
      "name": "file-operations",
      "enabled": true,
      "config": {...}
    }
  ]
}
```

##### `config/persistence_config.json` - 持久化配置
```json
{
  "database": {
    "type": "sqlite",
    "path": "data/agent_memory.db",
    "auto_create": true
  },
  "checkpointer": {
    "type": "AsyncSqliteSaver",
    "table_name": "checkpoints"
  }
}
```

##### `config/web_config.json` - Web服务配置
```json
{
  "server": {
    "host": "0.0.0.0",
    "port": 8000,
    "reload": false
  },
  "cors": {
    "allow_origins": ["*"],
    "allow_methods": ["*"],
    "allow_headers": ["*"]
  }
}
```

## 功能介绍

### 🧠 核心功能

#### 1. 智能对话系统
- **多轮对话**: 支持上下文相关的连续对话
- **记忆功能**: 自动保存和恢复对话历史
- **会话管理**: 支持多个独立会话并行
- **智能理解**: 基于大语言模型的自然语言理解

#### 2. 工具调用能力
- **35+个工具**: 涵盖文件操作、网络搜索、代码执行等
- **动态加载**: 运行时动态发现和加载工具
- **错误处理**: 工具调用失败时的优雅降级
- **配置管理**: 灵活的工具配置和启用/禁用

#### 3. 持久化存储
- **SQLite数据库**: 轻量级、高性能的本地存储
- **AsyncSqliteSaver**: LangGraph官方推荐的异步存储器
- **自动备份**: 定期备份重要数据
- **数据迁移**: 支持数据库结构升级

#### 4. 混合架构支持
- **CLI模式**: 传统命令行交互界面
- **Web API模式**: 现代Web服务接口
- **WebSocket**: 实时双向通信
- **统一核心**: 两种模式共享相同的核心逻辑

### 🌐 Web API功能

#### REST API端点
- `GET /health` - 系统健康检查
- `POST /api/chat` - 标准聊天接口
- `POST /api/chat/stream` - 流式聊天接口
- `GET /api/sessions/{thread_id}/history` - 获取会话历史
- `GET /api/tools` - 获取可用工具列表

#### WebSocket功能
- **实时通信**: 双向实时消息传递
- **会话隔离**: 每个WebSocket连接独立会话
- **流式响应**: 支持流式输出长文本
- **连接管理**: 自动处理连接断开和重连

#### API文档
- **自动生成**: FastAPI自动生成OpenAPI文档
- **交互式测试**: Swagger UI支持在线测试
- **类型安全**: Pydantic模型确保数据类型安全

### 🛠️ 开发功能

#### 测试框架
- **单元测试**: 核心组件的独立测试
- **集成测试**: 组件间交互测试
- **端到端测试**: 完整功能流程测试
- **性能测试**: 响应时间和并发能力测试

#### 错误处理
- **分层错误处理**: 不同层级的错误处理策略
- **错误分类**: 按类型和严重程度分类错误
- **日志记录**: 详细的错误日志和调试信息
- **恢复机制**: 自动错误恢复和重试

#### 配置管理
- **多环境支持**: 开发、测试、生产环境配置
- **环境变量**: 支持环境变量覆盖配置
- **热重载**: 配置文件修改后自动重载
- **验证机制**: 配置文件格式和内容验证

## 技术栈

### 🐍 核心技术栈
- **Python 3.11+**: 现代Python特性支持
- **LangChain**: 大语言模型应用开发框架
- **LangGraph**: 状态图和工作流管理
- **FastAPI**: 现代、快速的Web框架
- **WebSocket**: 实时双向通信协议
- **SQLite**: 轻量级关系数据库
- **Pydantic**: 数据验证和序列化
- **uvicorn**: ASGI服务器

### 📦 包管理
- **uv**: 现代Python包管理器
- **pyproject.toml**: 项目配置和依赖管理
- **虚拟环境**: 隔离的Python运行环境

### 🔧 开发工具
- **pytest**: 测试框架
- **black**: 代码格式化
- **flake8**: 代码质量检查
- **mypy**: 静态类型检查
- **pre-commit**: Git提交前检查

### 🏗️ 架构模式
- **四层架构**: 界面层、服务层、业务层、数据层
- **依赖注入**: 松耦合的组件设计
- **异步编程**: 高性能的异步I/O
- **事件驱动**: 基于事件的消息处理

## 部署指南

### 🚀 快速启动

#### 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd my_project

# 安装uv (如果未安装)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装依赖
uv sync
```

#### CLI模式启动
```bash
# 启动CLI交互模式
uv run main.py
```

#### Web API模式启动
```bash
# 启动Web API服务
uv run start_web.py

# 访问API文档
open http://localhost:8000/docs
```

### 🔧 配置说明

#### 环境变量
```bash
# LLM API密钥
export OPENAI_API_KEY="your-openai-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"

# 数据库路径
export DATABASE_PATH="data/agent_memory.db"

# Web服务端口
export WEB_PORT=8000
```

#### 配置文件修改
1. 编辑 `config/llm_config.json` 设置LLM提供商
2. 编辑 `config/mcp_config.json` 配置可用工具
3. 编辑 `config/web_config.json` 设置Web服务参数

### 📊 性能优化

#### 生产环境建议
- 使用 `gunicorn` 或 `uvicorn` 的多进程模式
- 配置反向代理 (nginx)
- 启用数据库连接池
- 配置日志轮转
- 设置监控和告警

#### 资源要求
- **最小配置**: 2GB RAM, 1 CPU核心
- **推荐配置**: 4GB RAM, 2 CPU核心
- **存储空间**: 至少1GB可用空间
- **网络**: 稳定的互联网连接 (用于LLM API调用)

---

*文档版本: 2.0.0*
*最后更新: 2025-06-26*
*维护者: LangGraph Agent Team*
