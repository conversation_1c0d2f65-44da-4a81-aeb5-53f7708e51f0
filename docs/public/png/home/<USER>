<svg width="20.000000" height="188.500000" viewBox="0 0 20 188.5" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_129_31324_dd" x="2.000000" y="0.000000" width="16.000000" height="187.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feGaussianBlur stdDeviation="0.666667" result="effect_layerBlur_1"/>
		</filter>
		<linearGradient x1="4.000000" y1="8.000000" x2="4.000000" y2="179.000000" id="paint_linear_129_31324_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF" stop-opacity="0.000000"/>
			<stop offset="0.301845" stop-color="#DE47FF" stop-opacity="0.466667"/>
			<stop offset="1.000000" stop-color="#0D86F9"/>
		</linearGradient>
		<linearGradient x1="8.000000" y1="179.000000" x2="8.000000" y2="8.000000" id="paint_linear_129_31325_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#FFFFFF" stop-opacity="0.000000"/>
		</linearGradient>
	</defs>
	<rect y="6.500000" width="20.000000" height="182.000000" fill="#C4C4C4" fill-opacity="0"/>
	<g filter="url(#filter_129_31324_dd)">
		<path d="M10 8L10 179" stroke="url(#paint_linear_129_31324_0)" stroke-opacity="1.000000" stroke-width="12.000000" stroke-linejoin="round" stroke-linecap="round"/>
	</g>
	<path d="M10 8L10 179" stroke="url(#paint_linear_129_31325_0)" stroke-opacity="1.000000" stroke-width="4.000000" stroke-linejoin="round" stroke-linecap="round"/>
</svg>
