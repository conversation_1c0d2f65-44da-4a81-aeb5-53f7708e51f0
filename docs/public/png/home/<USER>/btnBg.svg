<svg width="252.000000" height="84.000000" viewBox="0 0 252 84" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_207_36883_dd" x="-14.000000" y="-6.000000" width="280.000000" height="112.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="8"/>
			<feGaussianBlur stdDeviation="4.66667"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.71373 0 0 0 0 0.60392 0 0 0 0 1 0 0 0 0.25 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<clipPath id="clip207_36887">
			<rect id="线条按钮背景" width="252.000000" height="84.000000" fill="white" fill-opacity="0"/>
		</clipPath>
		<linearGradient x1="0.000000" y1="42.000000" x2="252.000015" y2="42.000000" id="paint_linear_207_36883_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#B16AFF"/>
			<stop offset="0.959047" stop-color="#7B79FF"/>
		</linearGradient>
	</defs>
	<g clip-path="url(#clip207_36887)">
		<g filter="url(#filter_207_36883_dd)">
			<rect id="矩形 1817" rx="42.000000" width="252.000015" height="84.000000" fill="#ECF3FF" fill-opacity="0"/>
			<rect id="矩形 1817" x="0.500000" y="0.500000" rx="41.500000" width="251.000015" height="83.000000" stroke="url(#paint_linear_207_36883_0)" stroke-opacity="1.000000" stroke-width="1.000000"/>
		</g>
	</g>
</svg>
